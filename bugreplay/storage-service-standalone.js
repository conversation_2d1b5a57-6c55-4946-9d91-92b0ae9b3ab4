/**
 * Standalone Storage Service for BugReplay Extension
 * 
 * This version works with Chrome extension service workers without ES6 modules
 * It provides IndexedDB storage with Chrome storage fallback
 */

/* global chrome, Dexie */

// Storage service configuration
const STORAGE_CONFIG = {
  DATABASE_NAME: 'BugReplayDB',
  DATABASE_VERSION: 1,
  CACHE_TTL: 5 * 60 * 1000, // 5 minutes
  MAX_CACHE_SIZE: 50,
  BATCH_SIZE: 20,
  STORAGE_QUOTA: 100 * 1024 * 1024, // 100MB
  CLEANUP_THRESHOLD: 0.8
};

// Global variables
let db = null;
let storageCache = new Map();
let isIndexedDBAvailable = false;

/**
 * Initialize IndexedDB database
 */
async function initializeDatabase() {
  try {
    // Check if Dexie is available
    if (typeof Dexie === 'undefined') {
      console.warn('BugReplay: Dexie not available, using Chrome storage fallback');
      return false;
    }

    // Create database
    db = new Dexie(STORAGE_CONFIG.DATABASE_NAME);
    
    // Define schema
    db.version(STORAGE_CONFIG.DATABASE_VERSION).stores({
      sessions: '++id, title, url, startTime, endTime, completedAt, status, size, imported',
      storageMetadata: '++id, type, data, lastUpdated',
      settings: '++id, key, value, lastModified'
    });

    // Open database
    await db.open();
    
    console.log('BugReplay: IndexedDB initialized successfully');
    isIndexedDBAvailable = true;
    return true;
  } catch (error) {
    console.warn('BugReplay: Failed to initialize IndexedDB:', error);
    isIndexedDBAvailable = false;
    return false;
  }
}

/**
 * Cache management
 */
function getCachedItem(key) {
  const item = storageCache.get(key);
  if (item && Date.now() - item.timestamp < STORAGE_CONFIG.CACHE_TTL) {
    return item.data;
  }
  storageCache.delete(key);
  return null;
}

function setCachedItem(key, data) {
  if (storageCache.size >= STORAGE_CONFIG.MAX_CACHE_SIZE) {
    // Remove oldest entries
    const entries = Array.from(storageCache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    for (let i = 0; i < Math.floor(STORAGE_CONFIG.MAX_CACHE_SIZE / 2); i++) {
      storageCache.delete(entries[i][0]);
    }
  }
  
  storageCache.set(key, {
    data,
    timestamp: Date.now()
  });
}

function clearCache() {
  storageCache.clear();
}

/**
 * Calculate session data size
 */
function calculateSessionSize(sessionData) {
  let size = 0;
  
  try {
    // Base session metadata
    const baseData = {
      id: sessionData.id,
      title: sessionData.title,
      url: sessionData.url,
      startTime: sessionData.startTime,
      endTime: sessionData.endTime,
      status: sessionData.status
    };
    size += JSON.stringify(baseData).length;

    // Add logs size
    if (sessionData.logs) {
      size += JSON.stringify(sessionData.logs).length;
    }

    // Add screenshots size
    if (sessionData.screenshots) {
      size += sessionData.screenshots.reduce((acc, screenshot) => {
        if (typeof screenshot === 'string') {
          return acc + screenshot.length;
        } else if (screenshot && screenshot.dataUrl) {
          return acc + screenshot.dataUrl.length;
        }
        return acc;
      }, 0);
    }

    // Add HAR data size
    if (sessionData.harData) {
      size += JSON.stringify(sessionData.harData).length;
    }

    // Add metadata size
    if (sessionData.metadata) {
      size += JSON.stringify(sessionData.metadata).length;
    }
  } catch (error) {
    console.warn('Error calculating session size:', error);
  }

  return size;
}

/**
 * Save session to storage
 */
async function saveSession(sessionData) {
  try {
    // Prepare session data
    const session = {
      ...sessionData,
      completedAt: sessionData.completedAt || new Date(),
      size: sessionData.size || calculateSessionSize(sessionData),
      duration: sessionData.endTime ? 
        new Date(sessionData.endTime) - new Date(sessionData.startTime) : 0,
      screenshotCount: sessionData.screenshots?.length || 0,
      logCount: sessionData.logs?.length || 0,
      networkRequestCount: sessionData.harData?.log?.entries?.length || 0
    };

    if (isIndexedDBAvailable && db) {
      // Save to IndexedDB
      await db.sessions.add(session);
      setCachedItem(session.id, session);
      console.log('BugReplay: Session saved to IndexedDB:', session.id);
      return { success: true, sessionId: session.id, size: session.size };
    } else {
      // Fallback to Chrome storage
      return await saveSessionChrome(session);
    }
  } catch (error) {
    console.error('Error saving session:', error);
    
    // Try Chrome storage fallback
    try {
      return await saveSessionChrome(sessionData);
    } catch (fallbackError) {
      return { success: false, error: fallbackError.message };
    }
  }
}

/**
 * Chrome storage fallback for saving sessions
 */
async function saveSessionChrome(sessionData) {
  try {
    // Get existing sessions list
    const result = await chrome.storage.local.get(['completedSessions']);
    const completedSessions = result.completedSessions || [];

    // Add new session to the list
    completedSessions.push({
      id: sessionData.id,
      title: sessionData.title,
      url: sessionData.url,
      startTime: sessionData.startTime,
      endTime: sessionData.endTime,
      completedAt: sessionData.completedAt,
      duration: sessionData.duration,
      size: sessionData.size,
      status: sessionData.status,
      screenshotCount: sessionData.screenshotCount,
      logCount: sessionData.logCount,
      networkRequestCount: sessionData.networkRequestCount
    });

    // Save both the session data and the updated list
    await chrome.storage.local.set({
      [`completed_session_${sessionData.id}`]: sessionData,
      'completedSessions': completedSessions
    });

    console.log('BugReplay: Session saved to Chrome storage:', sessionData.id);
    return { success: true, sessionId: sessionData.id, size: sessionData.size };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Get sessions from storage
 */
async function getSessions(options = {}) {
  try {
    const {
      offset = 0,
      limit = STORAGE_CONFIG.BATCH_SIZE,
      sortBy = 'completedAt',
      sortOrder = 'desc',
      search = '',
      status = null
    } = options;

    if (isIndexedDBAvailable && db) {
      // Get from IndexedDB
      let query = db.sessions.orderBy(sortBy);
      
      if (sortOrder === 'desc') {
        query = query.reverse();
      }

      // Apply filters
      if (status) {
        query = query.filter(session => session.status === status);
      }

      if (search) {
        const searchLower = search.toLowerCase();
        query = query.filter(session => 
          session.title?.toLowerCase().includes(searchLower) ||
          session.url?.toLowerCase().includes(searchLower) ||
          session.id.toLowerCase().includes(searchLower)
        );
      }

      // Get paginated results
      const sessions = await query.offset(offset).limit(limit).toArray();
      const totalCount = await query.count();

      // Convert dates from strings if needed
      const processedSessions = sessions.map(session => ({
        ...session,
        startTime: new Date(session.startTime),
        endTime: session.endTime ? new Date(session.endTime) : null,
        completedAt: new Date(session.completedAt)
      }));

      return {
        success: true,
        sessions: processedSessions,
        totalCount,
        hasMore: offset + limit < totalCount
      };
    } else {
      // Fallback to Chrome storage
      return await getSessionsChrome(options);
    }
  } catch (error) {
    console.error('Error getting sessions:', error);
    return { success: false, error: error.message, sessions: [] };
  }
}

/**
 * Chrome storage fallback for getting sessions
 */
async function getSessionsChrome(options = {}) {
  try {
    const result = await chrome.storage.local.get(['completedSessions']);
    let sessions = result.completedSessions || [];

    // Apply search filter
    if (options.search) {
      const searchLower = options.search.toLowerCase();
      sessions = sessions.filter(session =>
        session.title?.toLowerCase().includes(searchLower) ||
        session.url?.toLowerCase().includes(searchLower) ||
        session.id.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (options.status) {
      sessions = sessions.filter(session => session.status === options.status);
    }

    // Apply sorting
    const sortBy = options.sortBy || 'completedAt';
    const sortOrder = options.sortOrder || 'desc';
    
    sessions.sort((a, b) => {
      let aVal = a[sortBy];
      let bVal = b[sortBy];
      
      if (sortBy.includes('Time') || sortBy.includes('At')) {
        aVal = new Date(aVal);
        bVal = new Date(bVal);
      }
      
      if (sortOrder === 'asc') {
        return aVal > bVal ? 1 : -1;
      } else {
        return aVal < bVal ? 1 : -1;
      }
    });

    // Apply pagination
    const offset = options.offset || 0;
    const limit = options.limit || STORAGE_CONFIG.BATCH_SIZE;
    const paginatedSessions = sessions.slice(offset, offset + limit);

    return {
      success: true,
      sessions: paginatedSessions,
      totalCount: sessions.length,
      hasMore: offset + limit < sessions.length
    };
  } catch (error) {
    return { success: false, error: error.message, sessions: [] };
  }
}

/**
 * Get a specific session by ID
 */
async function getSession(sessionId) {
  try {
    // Check cache first
    const cached = getCachedItem(sessionId);
    if (cached) {
      return { success: true, session: cached };
    }

    if (isIndexedDBAvailable && db) {
      // Get from IndexedDB
      const session = await db.sessions.where('id').equals(sessionId).first();
      
      if (!session) {
        return { success: false, error: 'Session not found' };
      }

      // Convert dates and cache result
      const processedSession = {
        ...session,
        startTime: new Date(session.startTime),
        endTime: session.endTime ? new Date(session.endTime) : null,
        completedAt: new Date(session.completedAt)
      };

      setCachedItem(sessionId, processedSession);
      
      return { success: true, session: processedSession };
    } else {
      // Fallback to Chrome storage
      return await getSessionChrome(sessionId);
    }
  } catch (error) {
    console.error('Error getting session:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Chrome storage fallback for getting a session
 */
async function getSessionChrome(sessionId) {
  try {
    const result = await chrome.storage.local.get([`completed_session_${sessionId}`]);
    const session = result[`completed_session_${sessionId}`];
    
    if (!session) {
      return { success: false, error: 'Session not found' };
    }

    return { success: true, session };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Delete a session
 */
async function deleteSession(sessionId) {
  try {
    if (isIndexedDBAvailable && db) {
      const deletedCount = await db.sessions.where('id').equals(sessionId).delete();

      if (deletedCount === 0) {
        return { success: false, error: 'Session not found' };
      }

      // Remove from cache
      storageCache.delete(sessionId);
      console.log('BugReplay: Session deleted from IndexedDB:', sessionId);
      return { success: true };
    } else {
      // Fallback to Chrome storage
      return await deleteSessionChrome(sessionId);
    }
  } catch (error) {
    console.error('Error deleting session:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Chrome storage fallback for deleting a session
 */
async function deleteSessionChrome(sessionId) {
  try {
    // Remove session data
    await chrome.storage.local.remove([`completed_session_${sessionId}`]);

    // Update completed sessions list
    const result = await chrome.storage.local.get(['completedSessions']);
    const completedSessions = result.completedSessions || [];
    const updatedSessions = completedSessions.filter(session => session.id !== sessionId);
    await chrome.storage.local.set({ 'completedSessions': updatedSessions });

    console.log('BugReplay: Session deleted from Chrome storage:', sessionId);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Delete multiple sessions
 */
async function deleteSessions(sessionIds) {
  try {
    if (isIndexedDBAvailable && db) {
      const deletedCount = await db.sessions.where('id').anyOf(sessionIds).delete();

      // Remove from cache
      sessionIds.forEach(id => storageCache.delete(id));

      console.log(`BugReplay: ${deletedCount} sessions deleted from IndexedDB`);
      return { success: true, deletedCount };
    } else {
      // Fallback to individual deletions
      let deletedCount = 0;
      for (const sessionId of sessionIds) {
        const result = await deleteSession(sessionId);
        if (result.success) deletedCount++;
      }
      return { success: true, deletedCount };
    }
  } catch (error) {
    console.error('Error deleting sessions:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Get storage usage statistics
 */
async function getStorageUsage() {
  try {
    if (isIndexedDBAvailable && db) {
      const sessionCount = await db.sessions.count();
      const sessions = await db.sessions.toArray();
      const totalSessionSize = sessions.reduce((acc, session) => acc + (session.size || 0), 0);
      const averageSessionSize = sessionCount > 0 ? Math.round(totalSessionSize / sessionCount) : 0;

      // Estimate total database size (sessions + overhead)
      const totalSize = Math.round(totalSessionSize * 1.1);

      const usage = {
        used: totalSize,
        quota: STORAGE_CONFIG.STORAGE_QUOTA,
        available: STORAGE_CONFIG.STORAGE_QUOTA - totalSize,
        percentage: Math.round((totalSize / STORAGE_CONFIG.STORAGE_QUOTA) * 100),
        sessionCount,
        averageSessionSize,
        totalSessionSize
      };

      return { success: true, usage };
    } else {
      // Fallback to Chrome storage
      return await getStorageUsageChrome();
    }
  } catch (error) {
    console.error('Error getting storage usage:', error);
    return {
      success: false,
      error: error.message,
      usage: {
        used: 0,
        quota: STORAGE_CONFIG.STORAGE_QUOTA,
        available: STORAGE_CONFIG.STORAGE_QUOTA,
        percentage: 0,
        sessionCount: 0,
        averageSessionSize: 0,
        totalSessionSize: 0
      }
    };
  }
}

/**
 * Chrome storage fallback for getting storage usage
 */
async function getStorageUsageChrome() {
  try {
    const usage = await chrome.storage.local.getBytesInUse();
    const quota = chrome.storage.local.QUOTA_BYTES || 10485760; // 10MB default

    const result = await chrome.storage.local.get(['completedSessions']);
    const sessions = result.completedSessions || [];

    // Calculate actual session data sizes
    let totalSessionSize = 0;
    if (sessions.length > 0) {
      for (const session of sessions) {
        if (session.size) {
          totalSessionSize += session.size;
        } else {
          // Fallback: get the actual session data and calculate size
          const sessionResult = await chrome.storage.local.get([`completed_session_${session.id}`]);
          const sessionData = sessionResult[`completed_session_${session.id}`];
          if (sessionData) {
            const calculatedSize = calculateSessionSize(sessionData);
            totalSessionSize += calculatedSize;
          }
        }
      }
    }

    const usageData = {
      used: usage,
      quota: quota,
      available: quota - usage,
      percentage: Math.round((usage / quota) * 100),
      sessionCount: sessions.length,
      averageSessionSize: sessions.length > 0 ? Math.round(totalSessionSize / sessions.length) : 0,
      totalSessionSize: totalSessionSize
    };

    return { success: true, usage: usageData };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      usage: {
        used: 0,
        quota: 10485760,
        available: 10485760,
        percentage: 0,
        sessionCount: 0,
        averageSessionSize: 0,
        totalSessionSize: 0
      }
    };
  }
}

// Export functions for use in background script
window.BugReplayStorage = {
  initializeDatabase,
  saveSession,
  getSessions,
  getSession,
  deleteSession,
  deleteSessions,
  getStorageUsage,
  calculateSessionSize,
  clearCache,
  isIndexedDBAvailable: () => isIndexedDBAvailable
};
