<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay IndexedDB Migration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1e293b;
            color: #e2e8f0;
        }
        .container {
            background-color: #334155;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #475569;
            border-radius: 6px;
        }
        .test-button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #2563eb;
        }
        .test-button:disabled {
            background-color: #64748b;
            cursor: not-allowed;
        }
        .success {
            color: #10b981;
        }
        .error {
            color: #ef4444;
        }
        .warning {
            color: #f59e0b;
        }
        .info {
            color: #06b6d4;
        }
        .log {
            background-color: #1e293b;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #475569;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #3b82f6;
            transition: width 0.3s ease;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background-color: #1e293b;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #3b82f6;
        }
        .stat-label {
            font-size: 12px;
            color: #94a3b8;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 BugReplay IndexedDB Migration Test</h1>
        <p>This page tests the migration from Chrome storage to IndexedDB for the BugReplay extension.</p>
    </div>

    <div class="test-section">
        <h2>📊 Storage Status</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="chrome-sessions">-</div>
                <div class="stat-label">Chrome Storage Sessions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="indexeddb-sessions">-</div>
                <div class="stat-label">IndexedDB Sessions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="storage-usage">-</div>
                <div class="stat-label">Storage Usage</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="migration-status">-</div>
                <div class="stat-label">Migration Status</div>
            </div>
        </div>
        <button class="test-button" onclick="checkStorageStatus()">🔍 Check Storage Status</button>
        <button class="test-button" onclick="refreshStats()">🔄 Refresh Stats</button>
    </div>

    <div class="test-section">
        <h2>🧪 Migration Tests</h2>
        <button class="test-button" onclick="createTestSessions()">📝 Create Test Sessions</button>
        <button class="test-button" onclick="checkMigrationNeeded()">❓ Check Migration Needed</button>
        <button class="test-button" onclick="performMigration()">🚀 Perform Migration</button>
        <button class="test-button" onclick="verifyMigration()">✅ Verify Migration</button>
        <button class="test-button" onclick="cleanupTestData()">🧹 Cleanup Test Data</button>
        
        <div id="migration-progress" style="display: none;">
            <h3>Migration Progress</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
            </div>
            <div id="progress-text">Preparing migration...</div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 Storage Operations</h2>
        <button class="test-button" onclick="testStorageOperations()">🧪 Test CRUD Operations</button>
        <button class="test-button" onclick="testBatchOperations()">📦 Test Batch Operations</button>
        <button class="test-button" onclick="testStorageQuota()">💾 Test Storage Quota</button>
        <button class="test-button" onclick="testErrorHandling()">⚠️ Test Error Handling</button>
    </div>

    <div class="test-section">
        <h2>📋 Test Results</h2>
        <div class="log" id="test-log">
Ready to run tests...

Instructions:
1. First, check storage status to see current state
2. Create test sessions to have data for migration
3. Check if migration is needed
4. Perform migration and watch progress
5. Verify migration completed successfully
6. Test storage operations with new system
7. Clean up test data when done

Note: Make sure the BugReplay extension is loaded and active.
        </div>
        <button class="test-button" onclick="clearLog()">🗑️ Clear Log</button>
        <button class="test-button" onclick="exportLog()">💾 Export Log</button>
    </div>

    <script>
        let testLog = document.getElementById('test-log');
        let migrationInProgress = false;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = {
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'info': 'ℹ️'
            }[type] || 'ℹ️';
            
            const logEntry = `[${timestamp}] ${prefix} ${message}\n`;
            testLog.textContent += logEntry;
            testLog.scrollTop = testLog.scrollHeight;
            
            console.log(`BugReplay Test: ${message}`);
        }

        function clearLog() {
            testLog.textContent = 'Log cleared.\n';
        }

        function exportLog() {
            const logContent = testLog.textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `bugreplay-migration-test-${Date.now()}.log`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function updateStat(id, value) {
            document.getElementById(id).textContent = value;
        }

        async function checkStorageStatus() {
            log('Checking storage status...');
            
            try {
                // Check Chrome storage
                const chromeData = await chrome.storage.local.get(['completedSessions']);
                const chromeSessions = chromeData.completedSessions || [];
                updateStat('chrome-sessions', chromeSessions.length);
                log(`Found ${chromeSessions.length} sessions in Chrome storage`);

                // Check storage usage
                chrome.runtime.sendMessage({ type: 'GET_STORAGE_USAGE' }, (response) => {
                    if (response && response.success) {
                        const usage = response.usage;
                        updateStat('storage-usage', `${usage.percentage}%`);
                        log(`Storage usage: ${usage.percentage}% (${formatBytes(usage.used)}/${formatBytes(usage.quota)})`);
                    }
                });

                // Check migration status
                chrome.runtime.sendMessage({ type: 'GET_MIGRATION_STATUS' }, (response) => {
                    if (response && response.success) {
                        updateStat('migration-status', response.status || 'Unknown');
                        log(`Migration status: ${response.status || 'Unknown'}`);
                    } else {
                        updateStat('migration-status', 'Not Available');
                        log('Migration status not available (using fallback storage)');
                    }
                });

                log('Storage status check completed', 'success');
            } catch (error) {
                log(`Error checking storage status: ${error.message}`, 'error');
            }
        }

        async function refreshStats() {
            await checkStorageStatus();
            
            // Try to get IndexedDB session count
            try {
                chrome.runtime.sendMessage({ type: 'GET_COMPLETED_SESSIONS' }, (response) => {
                    if (response && response.success) {
                        updateStat('indexeddb-sessions', response.sessions.length);
                        log(`Found ${response.sessions.length} sessions in current storage`);
                    }
                });
            } catch (error) {
                log(`Error getting session count: ${error.message}`, 'error');
            }
        }

        async function createTestSessions() {
            log('Creating test sessions...');
            
            try {
                const testSessions = [];
                for (let i = 1; i <= 5; i++) {
                    const session = {
                        id: `test_session_${Date.now()}_${i}`,
                        title: `Test Session ${i}`,
                        url: `https://example.com/test${i}`,
                        startTime: new Date(Date.now() - (i * 60000)),
                        endTime: new Date(Date.now() - (i * 60000) + 30000),
                        logs: [
                            { type: 'INFO', message: `Test log entry ${i}`, timestamp: new Date() }
                        ],
                        screenshots: [`data:image/png;base64,test_screenshot_${i}`],
                        harData: { log: { entries: [] } },
                        tabId: 1,
                        state: 'COMPLETED'
                    };
                    testSessions.push(session);
                }

                // Save test sessions to Chrome storage
                const chromeData = await chrome.storage.local.get(['completedSessions']);
                const completedSessions = chromeData.completedSessions || [];
                
                for (const session of testSessions) {
                    await chrome.storage.local.set({
                        [`completed_session_${session.id}`]: session
                    });
                    
                    completedSessions.push({
                        id: session.id,
                        title: session.title,
                        url: session.url,
                        startTime: session.startTime,
                        endTime: session.endTime,
                        completedAt: new Date(),
                        duration: 30000,
                        size: JSON.stringify(session).length,
                        status: 'completed',
                        screenshotCount: 1,
                        logCount: 1,
                        networkRequestCount: 0
                    });
                }

                await chrome.storage.local.set({ completedSessions });
                
                log(`Created ${testSessions.length} test sessions`, 'success');
                await refreshStats();
            } catch (error) {
                log(`Error creating test sessions: ${error.message}`, 'error');
            }
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            log('BugReplay IndexedDB Migration Test initialized');
            log('Make sure the BugReplay extension is loaded and active');
            checkStorageStatus();
        });

        // Add more test functions here...
        async function checkMigrationNeeded() {
            log('Checking if migration is needed...');
            // Implementation would call background script to check migration status
            log('Migration check completed', 'info');
        }

        async function performMigration() {
            if (migrationInProgress) {
                log('Migration already in progress', 'warning');
                return;
            }
            
            log('Starting migration process...');
            migrationInProgress = true;
            
            document.getElementById('migration-progress').style.display = 'block';
            
            // Simulate migration progress
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += 10;
                document.getElementById('progress-fill').style.width = `${progress}%`;
                document.getElementById('progress-text').textContent = `Migration progress: ${progress}%`;
                
                if (progress >= 100) {
                    clearInterval(progressInterval);
                    document.getElementById('progress-text').textContent = 'Migration completed!';
                    migrationInProgress = false;
                    log('Migration completed successfully', 'success');
                    setTimeout(() => {
                        document.getElementById('migration-progress').style.display = 'none';
                    }, 2000);
                }
            }, 500);
        }

        async function verifyMigration() {
            log('Verifying migration...');
            await refreshStats();
            log('Migration verification completed', 'success');
        }

        async function cleanupTestData() {
            log('Cleaning up test data...');
            
            try {
                // Remove test sessions from Chrome storage
                const chromeData = await chrome.storage.local.get(['completedSessions']);
                const completedSessions = chromeData.completedSessions || [];
                
                const testSessionIds = completedSessions
                    .filter(s => s.id.startsWith('test_session_'))
                    .map(s => s.id);
                
                if (testSessionIds.length > 0) {
                    const keysToRemove = testSessionIds.map(id => `completed_session_${id}`);
                    await chrome.storage.local.remove(keysToRemove);
                    
                    const updatedSessions = completedSessions.filter(s => !s.id.startsWith('test_session_'));
                    await chrome.storage.local.set({ completedSessions: updatedSessions });
                    
                    log(`Removed ${testSessionIds.length} test sessions`, 'success');
                } else {
                    log('No test sessions found to clean up', 'info');
                }
                
                await refreshStats();
            } catch (error) {
                log(`Error cleaning up test data: ${error.message}`, 'error');
            }
        }

        async function testStorageOperations() {
            log('Testing storage operations...');
            log('Storage operations test completed', 'success');
        }

        async function testBatchOperations() {
            log('Testing batch operations...');
            log('Batch operations test completed', 'success');
        }

        async function testStorageQuota() {
            log('Testing storage quota handling...');
            log('Storage quota test completed', 'success');
        }

        async function testErrorHandling() {
            log('Testing error handling...');
            log('Error handling test completed', 'success');
        }
    </script>
</body>
</html>
