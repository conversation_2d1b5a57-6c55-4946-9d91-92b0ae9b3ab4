/* global chrome */

// content.js
let isCapturing = false;
let currentSessionId = null;
const originalConsole = {};
const originalFetch = window.fetch;
let mutationObserver = null;
let interactionListeners = [];
let lastScrollTime = 0;
let mediaRecorder = null;
let recordedChunks = [];
let currentUrl = window.location.href;
let currentTitle = document.title;

/**
 * Send log data to the extension popup and background script
 * @param {Object} logData - The log data to send
 */
function sendLog(logData) {
  const logEntry = {
    ...logData,
    timestamp: new Date().toISOString(), // Ensure timestamp is fresh
  };

  try {
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
      // Send to popup for real-time display
      chrome.runtime.sendMessage({
        type: 'LOG_ENTRY_FROM_CONTENT',
        payload: logEntry,
      });

      // Send to background script for persistence
      chrome.runtime.sendMessage({
        type: 'ADD_LOG_ENTRY',
        logEntry: logEntry,
      });
    } else {
      // console.warn('BugReplay: chrome.runtime.sendMessage not available to send log.');
    }
  } catch (error) {
    // console.warn('BugReplay: Could not send log, popup might be closed.', error);
    // This can happen if the popup is not open.
    // Logs are still sent to background script for persistence.
  }
}

/**
 * Override console methods to capture logs
 */
function overrideConsole() {
  ['log', 'warn', 'error', 'info', 'debug'].forEach((method) => {
    if (console[method]) {
      originalConsole[method] = console[method];
      console[method] = (...args) => {
        originalConsole[method](...args);
        if (isCapturing) {
          const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
          sendLog({
            type: method === 'error' ? 'CONSOLE_ERROR' : 'CONSOLE_LOG',
            message: `[Console.${method}] ${message}`,
            status: method === 'warn' ? 'warning' : undefined,
          });
        }
      };
    }
  });
}

/**
 * Restore original console methods
 */
function restoreConsole() {
  Object.keys(originalConsole).forEach((method) => {
    if (originalConsole[method]) {
      console[method] = originalConsole[method];
    }
  });
}

/**
 * Override fetch to capture network requests
 */
function overrideFetch() {
    window.fetch = async (...args) => {
        const [resource, config] = args;
        const requestUrl = resource instanceof Request ? resource.url : String(resource);
        const requestMethod = config?.method?.toUpperCase() || (resource instanceof Request ? resource.method.toUpperCase() : 'GET');

        if (isCapturing) {
            sendLog({
                type: 'NETWORK_REQUEST',
                message: `${requestMethod} ${requestUrl} - Initiated`,
                status: 'pending'
            });
        }

        try {
            const response = await originalFetch(...args);
            if (isCapturing) {
                sendLog({
                    type: 'NETWORK_REQUEST',
                    message: `${requestMethod} ${requestUrl} - ${response.status} ${response.statusText}`,
                    status: response.ok ? 'success' : 'error',
                });
            }
            return response;
        } catch (error) {
            if (isCapturing) {
                sendLog({
                    type: 'NETWORK_REQUEST',
                    message: `${requestMethod} ${requestUrl} - Failed: ${error.message}`,
                    status: 'error',
                });
            }
            throw error;
        }
    };
}

/**
 * Restore original fetch function
 */
function restoreFetch() {
    window.fetch = originalFetch;
}

/**
 * Get meaningful element description for user actions with enhanced context
 * @param {Element} element - DOM element
 * @returns {string} Human-readable description
 */
function getElementDescription(element) {
  const tagName = element.tagName.toLowerCase();

  // Enhanced button detection and description
  if (tagName === 'button' || (tagName === 'input' && ['button', 'submit', 'reset'].includes(element.type))) {
    const buttonText = element.textContent?.trim() || element.value || element.getAttribute('aria-label') || element.getAttribute('title');
    const buttonType = element.type || 'button';

    if (buttonText) {
      // Add button type context for submit/reset buttons
      if (buttonType === 'submit') {
        return `'${buttonText}' submit button`;
      } else if (buttonType === 'reset') {
        return `'${buttonText}' reset button`;
      } else {
        return `'${buttonText}' button`;
      }
    } else {
      // Fallback for buttons without text
      const iconClass = element.querySelector('i, svg')?.className || '';
      if (iconClass.includes('icon') || iconClass.includes('fa-')) {
        return `${buttonType} button with icon`;
      }
      return `${buttonType} button`;
    }
  }

  // Enhanced link detection with more context
  if (tagName === 'a') {
    const linkText = element.textContent?.trim() || element.getAttribute('aria-label') || element.getAttribute('title');
    const href = element.getAttribute('href');

    if (linkText && linkText.length > 0) {
      // Determine link type based on href
      if (href) {
        if (href.startsWith('mailto:')) {
          return `'${linkText}' email link`;
        } else if (href.startsWith('tel:')) {
          return `'${linkText}' phone link`;
        } else if (href.startsWith('#')) {
          return `'${linkText}' anchor link`;
        } else if (href.startsWith('http') && !href.includes(window.location.hostname)) {
          return `'${linkText}' external link`;
        } else {
          return `'${linkText}' link`;
        }
      } else {
        return `'${linkText}' link`;
      }
    } else if (href) {
      return `link to '${href.length > 50 ? href.substring(0, 47) + '...' : href}'`;
    }
    return 'link';
  }

  // Enhanced form input detection with better labeling
  if (tagName === 'input' || tagName === 'textarea' || tagName === 'select') {
    const inputType = element.type || 'text';
    const label = getInputLabel(element);
    const placeholder = element.getAttribute('placeholder');
    const name = element.getAttribute('name');

    // Special handling for different input types
    if (inputType === 'checkbox') {
      const labelText = label || placeholder || name || 'option';
      return `'${labelText}' checkbox`;
    } else if (inputType === 'radio') {
      const labelText = label || placeholder || name || 'option';
      const radioGroup = element.getAttribute('name');
      return radioGroup ? `'${labelText}' radio button in '${radioGroup}' group` : `'${labelText}' radio button`;
    } else if (inputType === 'file') {
      const labelText = label || 'file';
      return `'${labelText}' file upload`;
    } else if (inputType === 'range') {
      const labelText = label || 'slider';
      return `'${labelText}' slider`;
    } else if (inputType === 'color') {
      const labelText = label || 'color picker';
      return `'${labelText}' color picker`;
    } else if (inputType === 'date' || inputType === 'datetime-local' || inputType === 'time') {
      const labelText = label || inputType;
      return `'${labelText}' ${inputType} picker`;
    } else if (tagName === 'select') {
      const labelText = label || placeholder || name || 'dropdown';
      const isMultiple = element.hasAttribute('multiple');
      return isMultiple ? `'${labelText}' multi-select dropdown` : `'${labelText}' dropdown`;
    } else if (tagName === 'textarea') {
      const labelText = label || placeholder || name || 'text area';
      return `'${labelText}' text area`;
    } else {
      const labelText = label || placeholder || name || inputType;
      return `'${labelText}' ${inputType} field`;
    }
  }

  // Enhanced image detection
  if (tagName === 'img') {
    const alt = element.getAttribute('alt') || element.getAttribute('title');
    const src = element.getAttribute('src');
    if (alt) {
      return `'${alt}' image`;
    } else if (src) {
      const filename = src.split('/').pop()?.split('?')[0] || 'image';
      return `image '${filename}'`;
    }
    return 'image';
  }

  // Enhanced icon detection
  if (tagName === 'i' || tagName === 'svg' || element.classList.contains('icon')) {
    const iconClass = element.className;
    const ariaLabel = element.getAttribute('aria-label');
    const title = element.getAttribute('title');

    if (ariaLabel) {
      return `'${ariaLabel}' icon`;
    } else if (title) {
      return `'${title}' icon`;
    } else if (iconClass.includes('fa-')) {
      // FontAwesome icon detection
      const faClass = iconClass.split(' ').find(c => c.startsWith('fa-'));
      return `${faClass} icon`;
    }
    return 'icon';
  }

  // Enhanced semantic element detection
  const semanticElements = {
    'nav': 'navigation menu',
    'header': 'page header',
    'footer': 'page footer',
    'main': 'main content area',
    'aside': 'sidebar',
    'article': 'article',
    'section': 'section',
    'dialog': 'dialog',
    'modal': 'modal',
    'menu': 'menu',
    'menuitem': 'menu item',
    'tab': 'tab',
    'tabpanel': 'tab panel',
    'toolbar': 'toolbar',
    'tooltip': 'tooltip',
    'alert': 'alert',
    'status': 'status message'
  };

  // Check for ARIA roles
  const role = element.getAttribute('role');
  if (role && semanticElements[role]) {
    const ariaLabel = element.getAttribute('aria-label');
    return ariaLabel ? `'${ariaLabel}' ${semanticElements[role]}` : semanticElements[role];
  }

  if (semanticElements[tagName]) {
    const ariaLabel = element.getAttribute('aria-label');
    return ariaLabel ? `'${ariaLabel}' ${semanticElements[tagName]}` : semanticElements[tagName];
  }

  // Check for elements with aria-label
  const ariaLabel = element.getAttribute('aria-label');
  if (ariaLabel) {
    return `'${ariaLabel}' ${tagName}`;
  }

  // Check for elements with title
  const title = element.getAttribute('title');
  if (title) {
    return `${tagName} titled '${title}'`;
  }

  // Check for text content with better truncation
  const text = element.textContent?.trim();
  if (text && text.length > 0) {
    if (text.length <= 30) {
      return `${tagName} containing '${text}'`;
    } else {
      return `${tagName} containing '${text.substring(0, 27)}...'`;
    }
  }

  // Enhanced fallback with better class/id detection
  const id = element.id;
  const className = element.className;

  if (id) {
    return `${tagName} with id '${id}'`;
  } else if (className) {
    const meaningfulClasses = className.split(' ')
      .filter(c => c.trim() && !c.match(/^(css-|sc-|emotion-)/)) // Filter out CSS-in-JS classes
      .slice(0, 2)
      .join(' ');
    if (meaningfulClasses) {
      return `${tagName} with class '${meaningfulClasses}'`;
    }
  }

  return tagName;
}

/**
 * Find label for form input element
 * @param {Element} element - Input element
 * @returns {string|null} Label text
 */
function getInputLabel(element) {
  // Check for explicit label association
  const id = element.id;
  if (id) {
    const label = document.querySelector(`label[for="${id}"]`);
    if (label) {
      return label.textContent?.trim();
    }
  }

  // Check for parent label
  const parentLabel = element.closest('label');
  if (parentLabel) {
    return parentLabel.textContent?.trim();
  }

  // Check for aria-labelledby
  const labelledBy = element.getAttribute('aria-labelledby');
  if (labelledBy) {
    const labelElement = document.getElementById(labelledBy);
    if (labelElement) {
      return labelElement.textContent?.trim();
    }
  }

  // Check for nearby text (common pattern)
  const previousSibling = element.previousElementSibling;
  if (previousSibling && (previousSibling.tagName.toLowerCase() === 'label' || previousSibling.textContent?.trim())) {
    return previousSibling.textContent?.trim();
  }

  return null;
}

/**
 * Get contextual location description with enhanced detail
 * @param {Element} element - DOM element
 * @returns {string} Location context
 */
function getLocationContext(element) {
  const contexts = [];

  // Check for form context first (most specific)
  const form = element.closest('form');
  if (form) {
    const formName = form.getAttribute('name') || form.getAttribute('id');
    const formTitle = form.getAttribute('title') || form.getAttribute('aria-label');
    const formAction = form.getAttribute('action');

    if (formTitle) {
      contexts.push(`in '${formTitle}' form`);
    } else if (formName) {
      contexts.push(`in '${formName}' form`);
    } else if (formAction) {
      const actionName = formAction.split('/').pop() || 'form';
      contexts.push(`in ${actionName} form`);
    } else {
      contexts.push('in form');
    }
  }

  // Check for modal/dialog context
  const modal = element.closest('[role="dialog"], [role="modal"], .modal, .dialog');
  if (modal) {
    const modalTitle = modal.getAttribute('aria-label') || modal.getAttribute('title') ||
                     modal.querySelector('h1, h2, h3, .modal-title, .dialog-title')?.textContent?.trim();
    if (modalTitle) {
      contexts.push(`in '${modalTitle}' modal`);
    } else {
      contexts.push('in modal dialog');
    }
  }

  // Check for navigation context
  const nav = element.closest('nav, [role="navigation"]');
  if (nav) {
    const navLabel = nav.getAttribute('aria-label') || nav.getAttribute('title');
    if (navLabel) {
      contexts.push(`in '${navLabel}' navigation`);
    } else {
      // Try to identify navigation type by class or position
      const navClasses = nav.className.toLowerCase();
      if (navClasses.includes('main') || navClasses.includes('primary')) {
        contexts.push('in main navigation');
      } else if (navClasses.includes('secondary') || navClasses.includes('sub')) {
        contexts.push('in secondary navigation');
      } else if (navClasses.includes('breadcrumb')) {
        contexts.push('in breadcrumb navigation');
      } else {
        contexts.push('in navigation menu');
      }
    }
  }

  // Check for table context
  const table = element.closest('table');
  if (table) {
    const tableCaption = table.querySelector('caption')?.textContent?.trim();
    const tableTitle = table.getAttribute('title') || table.getAttribute('aria-label');
    if (tableCaption) {
      contexts.push(`in '${tableCaption}' table`);
    } else if (tableTitle) {
      contexts.push(`in '${tableTitle}' table`);
    } else {
      contexts.push('in table');
    }
  }

  // Check for list context
  const list = element.closest('ul, ol, dl');
  if (list && !nav) { // Don't double-report if already in nav
    const listTitle = list.getAttribute('aria-label') || list.getAttribute('title');
    const listType = list.tagName.toLowerCase() === 'ol' ? 'ordered list' :
                    list.tagName.toLowerCase() === 'dl' ? 'definition list' : 'list';
    if (listTitle) {
      contexts.push(`in '${listTitle}' ${listType}`);
    } else {
      contexts.push(`in ${listType}`);
    }
  }

  // Check for section/article context
  const section = element.closest('section, article');
  if (section) {
    const sectionTitle = section.getAttribute('aria-label') || section.getAttribute('title') ||
                        section.querySelector('h1, h2, h3, h4, h5, h6')?.textContent?.trim();
    const sectionType = section.tagName.toLowerCase();
    if (sectionTitle) {
      contexts.push(`in '${sectionTitle}' ${sectionType}`);
    } else {
      contexts.push(`in ${sectionType}`);
    }
  }

  // Check for semantic regions
  const semanticRegions = [
    { selector: 'header', name: 'page header' },
    { selector: 'footer', name: 'page footer' },
    { selector: 'aside', name: 'sidebar' },
    { selector: 'main', name: 'main content area' },
    { selector: '[role="banner"]', name: 'banner' },
    { selector: '[role="contentinfo"]', name: 'content info' },
    { selector: '[role="complementary"]', name: 'complementary content' },
    { selector: '[role="main"]', name: 'main content' }
  ];

  for (const region of semanticRegions) {
    const regionElement = element.closest(region.selector);
    if (regionElement) {
      const regionLabel = regionElement.getAttribute('aria-label') || regionElement.getAttribute('title');
      if (regionLabel) {
        contexts.push(`in '${regionLabel}' ${region.name}`);
      } else {
        contexts.push(`in ${region.name}`);
      }
      break; // Only add one semantic region
    }
  }

  // Return the most specific context (usually the first one found)
  return contexts.length > 0 ? contexts[0] : '';
}

/**
 * Handle user click actions with enhanced contextual information
 * @param {Event} event - Click event
 */
function handleUserAction(event) {
  if (!isCapturing || !event.target) return;

  const target = event.target;
  const elementDescription = getElementDescription(target);
  const locationContext = getLocationContext(target);

  // Enhanced description with action context
  let description = `Clicked ${elementDescription}`;
  if (locationContext) {
    description += ` ${locationContext}`;
  }

  // Add additional context for specific element types
  const additionalContext = getClickContext(target, event);
  if (additionalContext) {
    description += ` ${additionalContext}`;
  }

  const interaction = {
    type: 'click',
    target: generateSelector(target),
    coordinates: { x: event.clientX, y: event.clientY },
    timestamp: new Date(),
    description: description,
    elementText: target.textContent?.trim().substring(0, 100) || '',
    elementType: target.tagName.toLowerCase(),
    elementId: target.id || '',
    elementClasses: target.className || '',
    // Enhanced metadata
    elementRole: target.getAttribute('role') || '',
    elementAriaLabel: target.getAttribute('aria-label') || '',
    elementTitle: target.getAttribute('title') || '',
    elementValue: target.value || '',
    elementHref: target.getAttribute('href') || '',
    elementSrc: target.getAttribute('src') || '',
    clickType: event.detail > 1 ? 'double-click' : 'single-click',
    modifierKeys: {
      ctrl: event.ctrlKey,
      alt: event.altKey,
      shift: event.shiftKey,
      meta: event.metaKey
    }
  };

  sendLog({
    type: 'USER_ACTION',
    message: description,
    interaction: interaction
  });
}

/**
 * Get additional context for click actions
 * @param {Element} target - The clicked element
 * @param {Event} event - The click event
 * @returns {string} Additional context information
 */
function getClickContext(target, event) {
  const contexts = [];

  // Check for modifier keys
  if (event.ctrlKey || event.metaKey) {
    contexts.push('(with Ctrl/Cmd key)');
  }
  if (event.shiftKey) {
    contexts.push('(with Shift key)');
  }
  if (event.altKey) {
    contexts.push('(with Alt key)');
  }

  // Check for double-click
  if (event.detail > 1) {
    contexts.push('(double-click)');
  }

  // Check for right-click
  if (event.button === 2) {
    contexts.push('(right-click)');
  }

  // Check if it's a link with destination
  if (target.tagName.toLowerCase() === 'a' && target.href) {
    const href = target.href;
    if (href.startsWith('mailto:')) {
      contexts.push(`(opens email to ${href.replace('mailto:', '')})`);
    } else if (href.startsWith('tel:')) {
      contexts.push(`(calls ${href.replace('tel:', '')})`);
    } else if (href.startsWith('#')) {
      contexts.push('(navigates to page section)');
    } else if (!href.includes(window.location.hostname)) {
      contexts.push('(opens external link)');
    } else {
      contexts.push('(navigates to new page)');
    }
  }

  // Check for form submission context
  if (target.type === 'submit' || (target.tagName.toLowerCase() === 'button' && target.form)) {
    contexts.push('(submits form)');
  }

  // Check for file input
  if (target.type === 'file') {
    contexts.push('(opens file picker)');
  }

  // Check for checkbox/radio state
  if (target.type === 'checkbox') {
    contexts.push(target.checked ? '(unchecks option)' : '(checks option)');
  }
  if (target.type === 'radio') {
    contexts.push('(selects option)');
  }

  return contexts.join(' ');
}

/**
 * Handle navigation events and page changes
 */
function handleNavigation() {
  if (!isCapturing) return;

  const newUrl = window.location.href;
  const newTitle = document.title;

  // Check if URL changed
  if (newUrl !== currentUrl) {
    const navigationTrigger = determineNavigationTrigger(currentUrl, newUrl);

    let description = `Navigated from '${getPageDescription(currentUrl, currentTitle)}' to '${getPageDescription(newUrl, newTitle)}'`;
    if (navigationTrigger) {
      description += ` ${navigationTrigger}`;
    }

    const interaction = {
      type: 'navigation',
      fromUrl: currentUrl,
      toUrl: newUrl,
      fromTitle: currentTitle,
      toTitle: newTitle,
      timestamp: new Date(),
      description: description,
      trigger: navigationTrigger,
      referrer: document.referrer || ''
    };

    sendLog({
      type: 'USER_ACTION',
      message: description,
      interaction: interaction
    });

    // Update current state
    currentUrl = newUrl;
    currentTitle = newTitle;
  }
  // Check if only title changed (SPA navigation)
  else if (newTitle !== currentTitle) {
    const description = `Page title changed from '${currentTitle}' to '${newTitle}' (SPA navigation)`;

    const interaction = {
      type: 'title_change',
      url: newUrl,
      fromTitle: currentTitle,
      toTitle: newTitle,
      timestamp: new Date(),
      description: description,
      trigger: 'spa_navigation'
    };

    sendLog({
      type: 'USER_ACTION',
      message: description,
      interaction: interaction
    });

    currentTitle = newTitle;
  }
}

/**
 * Determine what triggered the navigation
 * @param {string} fromUrl - Previous URL
 * @param {string} toUrl - New URL
 * @returns {string} Navigation trigger description
 */
function determineNavigationTrigger(fromUrl, toUrl) {
  // Check if it's a hash change (anchor navigation)
  if (fromUrl.split('#')[0] === toUrl.split('#')[0]) {
    return '(anchor navigation)';
  }

  // Check if it's a query parameter change
  const fromBase = fromUrl.split('?')[0];
  const toBase = toUrl.split('?')[0];
  if (fromBase === toBase) {
    return '(query parameter change)';
  }

  // Check if it's a subdirectory navigation
  if (toUrl.startsWith(fromUrl) || fromUrl.startsWith(toUrl)) {
    return '(subdirectory navigation)';
  }

  // Check if it's external navigation
  const fromDomain = new URL(fromUrl).hostname;
  const toDomain = new URL(toUrl).hostname;
  if (fromDomain !== toDomain) {
    return '(external navigation)';
  }

  return '(page navigation)';
}

/**
 * Get a readable page description
 * @param {string} url - Page URL
 * @param {string} title - Page title
 * @returns {string} Page description
 */
function getPageDescription(url, title) {
  if (title && title.trim() && title !== 'Untitled') {
    return title.length > 50 ? title.substring(0, 47) + '...' : title;
  }

  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    if (pathname === '/' || pathname === '') {
      return urlObj.hostname + ' homepage';
    }

    // Extract meaningful path segments
    const segments = pathname.split('/').filter(s => s.length > 0);
    if (segments.length > 0) {
      const lastSegment = segments[segments.length - 1];
      return lastSegment.replace(/[-_]/g, ' ').replace(/\.[^.]*$/, ''); // Remove file extension
    }

    return urlObj.hostname;
  } catch (error) {
    return url.length > 50 ? url.substring(0, 47) + '...' : url;
  }
}

/**
 * Handle scroll events
 * @param {Event} event - Scroll event
 */
function handleScrollAction(event) {
  if (!isCapturing) return;

  const now = Date.now();
  if (now - lastScrollTime < 250) return; // Throttle scroll events (reduced from 100ms to capture more meaningful scrolls)
  lastScrollTime = now;

  const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
  const scrollY = window.pageYOffset || document.documentElement.scrollTop;

  // Calculate scroll direction and distance
  const scrollDirection = getScrollDirection(scrollY);
  const scrollPercentage = getScrollPercentage();

  // Create more descriptive scroll messages for bug reports
  let description;
  if (scrollPercentage !== null) {
    if (scrollDirection === 'down') {
      description = `Scrolled down to ${scrollPercentage}% of page`;
    } else if (scrollDirection === 'up') {
      description = `Scrolled up to ${scrollPercentage}% of page`;
    } else {
      description = `Scrolled horizontally to position ${scrollX}, ${scrollY}`;
    }
  } else {
    description = `Scrolled ${scrollDirection} on page`;
  }

  // Add position details for debugging if needed
  description += ` (position: ${scrollX}, ${scrollY})`;

  const interaction = {
    type: 'scroll',
    target: 'window',
    coordinates: { x: scrollX, y: scrollY },
    timestamp: new Date(),
    description: description,
    scrollDirection: scrollDirection,
    scrollPercentage: scrollPercentage
  };

  sendLog({
    type: 'USER_ACTION',
    message: description,
    interaction: interaction
  });
}

/**
 * Determine scroll direction
 * @param {number} currentScrollY - Current scroll position
 * @returns {string} Scroll direction
 */
function getScrollDirection(currentScrollY) {
  const lastScrollY = window.lastScrollY || 0;
  window.lastScrollY = currentScrollY;

  if (currentScrollY > lastScrollY) {
    return 'down';
  } else if (currentScrollY < lastScrollY) {
    return 'up';
  }
  return 'horizontally';
}

/**
 * Calculate scroll percentage
 * @returns {number|null} Scroll percentage
 */
function getScrollPercentage() {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;

  if (scrollHeight <= 0) return null;

  return Math.round((scrollTop / scrollHeight) * 100);
}

/**
 * Check if a field contains sensitive data that should not be logged
 * @param {Element} element - Form field element
 * @returns {boolean} True if field is sensitive
 */
function isSensitiveField(element) {
  const type = element.type?.toLowerCase() || '';
  const name = element.name?.toLowerCase() || '';
  const id = element.id?.toLowerCase() || '';
  const autocomplete = element.autocomplete?.toLowerCase() || '';
  const className = element.className?.toLowerCase() || '';

  // Check for password fields
  if (type === 'password') return true;

  // Check for credit card fields
  const creditCardPatterns = ['card', 'credit', 'ccv', 'cvv', 'cvc', 'expir'];
  if (creditCardPatterns.some(pattern =>
    name.includes(pattern) || id.includes(pattern) || autocomplete.includes(pattern) || className.includes(pattern)
  )) return true;

  // Check for SSN fields
  const ssnPatterns = ['ssn', 'social', 'security'];
  if (ssnPatterns.some(pattern =>
    name.includes(pattern) || id.includes(pattern) || autocomplete.includes(pattern)
  )) return true;

  // Check for other sensitive fields
  const sensitivePatterns = ['pin', 'secret', 'token', 'key', 'auth'];
  if (sensitivePatterns.some(pattern =>
    name.includes(pattern) || id.includes(pattern) || autocomplete.includes(pattern)
  )) return true;

  return false;
}

/**
 * Handle keyboard input with enhanced form interaction tracking
 * @param {Event} event - Keyboard event
 */
function handleKeyboardAction(event) {
  if (!isCapturing) return;

  const target = event.target;
  const tagName = target.tagName.toLowerCase();
  const type = target.type || '';

  // Track meaningful keyboard interactions in form elements
  if (tagName === 'input' || tagName === 'textarea' || tagName === 'select') {
    const fieldDescription = getElementDescription(target);
    const locationContext = getLocationContext(target);

    let description;
    let actionType = 'keypress';

    if (event.key === 'Enter') {
      if (target.form && (type === 'submit' || tagName === 'button')) {
        description = `Pressed Enter to submit form via ${fieldDescription}`;
        actionType = 'form_submit_key';
      } else {
        description = `Pressed Enter in ${fieldDescription}`;
      }
    } else if (event.key === 'Tab') {
      description = `Pressed Tab to navigate from ${fieldDescription}`;
      actionType = 'field_navigation';
    } else if (event.key === 'Escape') {
      description = `Pressed Escape in ${fieldDescription}`;
      actionType = 'field_escape';
    } else if (event.key === 'Backspace' || event.key === 'Delete') {
      description = `Deleted text in ${fieldDescription}`;
      actionType = 'text_deletion';
    } else if (event.key.length === 1 && !event.ctrlKey && !event.metaKey) {
      // Only log actual text input, not every keystroke
      if (isSensitiveField(target)) {
        description = `Entered text in ${fieldDescription} (content hidden for privacy)`;
      } else {
        description = `Typed in ${fieldDescription}`;
      }
      actionType = 'text_input';
    } else if (event.key.startsWith('Arrow')) {
      description = `Used ${event.key} to navigate in ${fieldDescription}`;
      actionType = 'field_navigation';
    } else {
      description = `Pressed ${event.key} in ${fieldDescription}`;
    }

    if (locationContext) {
      description += ` ${locationContext}`;
    }

    const interaction = {
      type: actionType,
      target: generateSelector(target),
      key: event.key,
      value: isSensitiveField(target) ? '[HIDDEN]' : target.value,
      timestamp: new Date(),
      description: description,
      fieldType: type,
      fieldLabel: getInputLabel(target) || '',
      fieldName: target.name || '',
      isSpecialKey: event.key.length > 1,
      isSensitive: isSensitiveField(target),
      modifierKeys: {
        ctrl: event.ctrlKey,
        alt: event.altKey,
        shift: event.shiftKey,
        meta: event.metaKey
      }
    };

    sendLog({
      type: 'USER_ACTION',
      message: description,
      interaction: interaction
    });
  }
}

/**
 * Handle form submissions
 * @param {Event} event - Submit event
 */
function handleFormSubmission(event) {
  if (!isCapturing) return;

  const form = event.target;
  const action = form.action || window.location.href;
  const method = form.method || 'GET';

  // Get form description
  const formName = form.getAttribute('name') || form.getAttribute('id');
  const formTitle = form.getAttribute('title') || form.getAttribute('aria-label');

  let formDescription = 'form';
  if (formTitle) {
    formDescription = `'${formTitle}' form`;
  } else if (formName) {
    formDescription = `'${formName}' form`;
  }

  // Get form context
  const locationContext = getLocationContext(form);

  // Count form fields
  const inputs = form.querySelectorAll('input, textarea, select');
  const fieldCount = inputs.length;

  let description = `User submitted ${formDescription}`;
  if (fieldCount > 0) {
    description += ` with ${fieldCount} field${fieldCount !== 1 ? 's' : ''}`;
  }
  if (locationContext) {
    description += ` ${locationContext}`;
  }
  description += ` (${method.toUpperCase()} to ${action})`;

  const interaction = {
    type: 'submit',
    target: generateSelector(form),
    action: action,
    method: method,
    timestamp: new Date(),
    description: description,
    formName: formName || '',
    formTitle: formTitle || '',
    fieldCount: fieldCount
  };

  sendLog({
    type: 'USER_ACTION',
    message: description,
    interaction: interaction
  });
}

/**
 * Handle form field focus events
 * @param {Event} event - Focus event
 */
function handleFieldFocus(event) {
  if (!isCapturing || !event.target) return;

  const target = event.target;
  const tagName = target.tagName.toLowerCase();

  if (tagName === 'input' || tagName === 'textarea' || tagName === 'select') {
    const fieldDescription = getElementDescription(target);
    const locationContext = getLocationContext(target);

    let description = `Focused on ${fieldDescription}`;
    if (locationContext) {
      description += ` ${locationContext}`;
    }

    const interaction = {
      type: 'field_focus',
      target: generateSelector(target),
      timestamp: new Date(),
      description: description,
      fieldType: target.type || tagName,
      fieldLabel: getInputLabel(target) || '',
      fieldName: target.name || '',
      fieldValue: isSensitiveField(target) ? '[HIDDEN]' : target.value
    };

    sendLog({
      type: 'USER_ACTION',
      message: description,
      interaction: interaction
    });
  }
}

/**
 * Handle form field change events (for dropdowns, checkboxes, etc.)
 * @param {Event} event - Change event
 */
function handleFieldChange(event) {
  if (!isCapturing || !event.target) return;

  const target = event.target;
  const tagName = target.tagName.toLowerCase();
  const type = target.type || '';

  if (tagName === 'input' || tagName === 'textarea' || tagName === 'select') {
    const fieldDescription = getElementDescription(target);
    const locationContext = getLocationContext(target);

    let description;
    let newValue = target.value;

    if (type === 'checkbox') {
      description = `${target.checked ? 'Checked' : 'Unchecked'} ${fieldDescription}`;
      newValue = target.checked ? 'checked' : 'unchecked';
    } else if (type === 'radio') {
      description = `Selected ${fieldDescription}`;
      newValue = target.value;
    } else if (tagName === 'select') {
      const selectedOption = target.options[target.selectedIndex];
      const optionText = selectedOption ? selectedOption.textContent.trim() : target.value;
      description = `Selected '${optionText}' in ${fieldDescription}`;
      newValue = optionText;
    } else if (type === 'file') {
      const fileCount = target.files ? target.files.length : 0;
      if (fileCount > 0) {
        const fileNames = Array.from(target.files).map(f => f.name).join(', ');
        description = `Selected ${fileCount} file${fileCount > 1 ? 's' : ''} in ${fieldDescription}: ${fileNames}`;
        newValue = fileNames;
      } else {
        description = `Cleared file selection in ${fieldDescription}`;
        newValue = '';
      }
    } else if (type === 'range') {
      description = `Set ${fieldDescription} to ${target.value}`;
      newValue = target.value;
    } else if (type === 'color') {
      description = `Selected color ${target.value} in ${fieldDescription}`;
      newValue = target.value;
    } else {
      if (isSensitiveField(target)) {
        description = `Modified ${fieldDescription} (content hidden for privacy)`;
        newValue = '[HIDDEN]';
      } else {
        description = `Modified ${fieldDescription}`;
        newValue = target.value;
      }
    }

    if (locationContext) {
      description += ` ${locationContext}`;
    }

    const interaction = {
      type: 'field_change',
      target: generateSelector(target),
      timestamp: new Date(),
      description: description,
      fieldType: type || tagName,
      fieldLabel: getInputLabel(target) || '',
      fieldName: target.name || '',
      newValue: newValue,
      isSensitive: isSensitiveField(target)
    };

    sendLog({
      type: 'USER_ACTION',
      message: description,
      interaction: interaction
    });
  }
}

/**
 * Handle text selection events
 */
function handleTextSelection() {
  if (!isCapturing) return;

  const selection = window.getSelection();
  if (!selection || selection.isCollapsed || selection.toString().trim().length === 0) {
    return;
  }

  const selectedText = selection.toString().trim();
  const range = selection.getRangeAt(0);
  const container = range.commonAncestorContainer;
  const element = container.nodeType === Node.TEXT_NODE ? container.parentElement : container;

  // Don't log selections in sensitive fields
  if (element && isSensitiveField(element)) {
    return;
  }

  const elementDescription = element ? getElementDescription(element) : 'page content';
  const locationContext = element ? getLocationContext(element) : '';

  let description = `Selected text: "${selectedText.length > 50 ? selectedText.substring(0, 47) + '...' : selectedText}"`;
  if (elementDescription !== 'page content') {
    description += ` in ${elementDescription}`;
  }
  if (locationContext) {
    description += ` ${locationContext}`;
  }

  const interaction = {
    type: 'text_selection',
    target: element ? generateSelector(element) : 'document',
    selectedText: selectedText.length > 200 ? selectedText.substring(0, 197) + '...' : selectedText,
    selectionLength: selectedText.length,
    timestamp: new Date(),
    description: description,
    startOffset: range.startOffset,
    endOffset: range.endOffset
  };

  sendLog({
    type: 'USER_ACTION',
    message: description,
    interaction: interaction
  });
}

/**
 * Handle drag and drop events
 * @param {Event} event - Drag event
 */
function handleDragDrop(event) {
  if (!isCapturing) return;

  const target = event.target;
  const eventType = event.type;

  if (eventType === 'dragstart') {
    const elementDescription = getElementDescription(target);
    const locationContext = getLocationContext(target);

    let description = `Started dragging ${elementDescription}`;
    if (locationContext) {
      description += ` ${locationContext}`;
    }

    const interaction = {
      type: 'drag_start',
      target: generateSelector(target),
      timestamp: new Date(),
      description: description,
      elementType: target.tagName.toLowerCase(),
      dragData: event.dataTransfer ? event.dataTransfer.types : []
    };

    sendLog({
      type: 'USER_ACTION',
      message: description,
      interaction: interaction
    });
  } else if (eventType === 'drop') {
    const elementDescription = getElementDescription(target);
    const locationContext = getLocationContext(target);

    let description = `Dropped item on ${elementDescription}`;
    if (locationContext) {
      description += ` ${locationContext}`;
    }

    // Handle file drops
    if (event.dataTransfer && event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      const fileNames = Array.from(event.dataTransfer.files).map(f => f.name).join(', ');
      description += ` (files: ${fileNames})`;
    }

    const interaction = {
      type: 'drop',
      target: generateSelector(target),
      timestamp: new Date(),
      description: description,
      elementType: target.tagName.toLowerCase(),
      dropData: event.dataTransfer ? event.dataTransfer.types : [],
      fileCount: event.dataTransfer && event.dataTransfer.files ? event.dataTransfer.files.length : 0
    };

    sendLog({
      type: 'USER_ACTION',
      message: description,
      interaction: interaction
    });
  }
}

/**
 * Generate CSS selector for an element with semantic information
 * @param {Element} element - DOM element
 * @returns {string} CSS selector
 */
function generateSelector(element) {
  // Priority 1: Use ID if available
  if (element.id) {
    return `#${element.id}`;
  }

  // Priority 2: Use semantic attributes for better selectors
  const tagName = element.tagName.toLowerCase();

  // For buttons, try to use text content or value
  if (tagName === 'button' || (tagName === 'input' && ['button', 'submit'].includes(element.type))) {
    const buttonText = element.textContent?.trim() || element.value;
    if (buttonText) {
      // Create a selector that includes text for better identification
      const textSelector = buttonText.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
      if (textSelector) {
        return `${tagName}[data-text*="${textSelector}"], ${tagName}:contains("${buttonText}")`;
      }
    }
  }

  // For links, try to use href or text
  if (tagName === 'a') {
    const href = element.getAttribute('href');
    const linkText = element.textContent?.trim();
    if (href) {
      return `a[href="${href}"]`;
    } else if (linkText) {
      const textSelector = linkText.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
      if (textSelector) {
        return `a:contains("${linkText}")`;
      }
    }
  }

  // For form inputs, try to use name, type, or associated label
  if (['input', 'textarea', 'select'].includes(tagName)) {
    const name = element.getAttribute('name');
    const type = element.getAttribute('type');
    const placeholder = element.getAttribute('placeholder');

    if (name) {
      return `${tagName}[name="${name}"]`;
    } else if (type && tagName === 'input') {
      return `input[type="${type}"]`;
    } else if (placeholder) {
      return `${tagName}[placeholder="${placeholder}"]`;
    }
  }

  // Priority 3: Use meaningful class names
  if (element.className) {
    const classes = element.className.split(' ').filter(c => c.trim());
    // Prioritize semantic class names
    const semanticClasses = classes.filter(c =>
      c.includes('btn') || c.includes('button') || c.includes('link') ||
      c.includes('nav') || c.includes('menu') || c.includes('form') ||
      c.includes('input') || c.includes('field') || c.includes('submit')
    );

    if (semanticClasses.length > 0) {
      return `${tagName}.${semanticClasses.slice(0, 2).join('.')}`;
    } else if (classes.length > 0) {
      return `${tagName}.${classes.slice(0, 2).join('.')}`;
    }
  }

  // Priority 4: Use aria-label or role
  const ariaLabel = element.getAttribute('aria-label');
  const role = element.getAttribute('role');
  if (ariaLabel) {
    return `${tagName}[aria-label="${ariaLabel}"]`;
  } else if (role) {
    return `${tagName}[role="${role}"]`;
  }

  // Priority 5: Generate path-based selector with semantic context
  const path = [];
  let current = element;

  while (current && current.nodeType === Node.ELEMENT_NODE) {
    let selector = current.tagName.toLowerCase();

    // Add semantic context to path elements
    if (current.id) {
      selector += `#${current.id}`;
      path.unshift(selector);
      break;
    }

    // Add meaningful attributes to path
    const role = current.getAttribute('role');
    const ariaLabel = current.getAttribute('aria-label');
    if (role) {
      selector += `[role="${role}"]`;
    } else if (ariaLabel) {
      selector += `[aria-label="${ariaLabel}"]`;
    } else if (current.className) {
      const classes = current.className.split(' ').filter(c => c.trim());
      const semanticClass = classes.find(c =>
        c.includes('nav') || c.includes('menu') || c.includes('header') ||
        c.includes('footer') || c.includes('main') || c.includes('content')
      );
      if (semanticClass) {
        selector += `.${semanticClass}`;
      } else if (classes.length > 0) {
        selector += `.${classes[0]}`;
      }
    }

    // Add nth-child only if necessary
    const siblings = Array.from(current.parentNode?.children || []);
    const sameTagSiblings = siblings.filter(s => s.tagName === current.tagName);
    if (sameTagSiblings.length > 1) {
      const index = sameTagSiblings.indexOf(current);
      selector += `:nth-of-type(${index + 1})`;
    }

    path.unshift(selector);
    current = current.parentNode;

    if (path.length > 4) break; // Limit depth for readability
  }

  return path.join(' > ');
}

/**
 * Initialize DOM mutation observer
 */
function initializeMutationObserver() {
  mutationObserver = new MutationObserver((mutations) => {
    if (!isCapturing) return;

    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        const addedElements = Array.from(mutation.addedNodes)
          .filter(node => node.nodeType === Node.ELEMENT_NODE)
          .map(node => node.tagName?.toLowerCase())
          .filter(Boolean);

        if (addedElements.length > 0) {
          sendLog({
            type: 'DOM_MUTATION',
            message: `DOM elements added: ${addedElements.join(', ')}`,
            timestamp: new Date()
          });
        }
      }

      if (mutation.type === 'attributes') {
        sendLog({
          type: 'DOM_MUTATION',
          message: `Attribute '${mutation.attributeName}' changed on ${mutation.target.tagName?.toLowerCase()}`,
          timestamp: new Date()
        });
      }
    });
  });

  mutationObserver.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['class', 'style', 'data-*']
  });
}

/**
 * Setup all interaction listeners with enhanced tracking
 */
function setupInteractionListeners() {
  const listeners = [
    // Core interaction events
    { event: 'click', handler: handleUserAction, options: true },
    { event: 'scroll', handler: handleScrollAction, options: false },
    { event: 'keydown', handler: handleKeyboardAction, options: false },
    { event: 'submit', handler: handleFormSubmission, options: false },

    // Enhanced form interaction events
    { event: 'focus', handler: handleFieldFocus, options: true },
    { event: 'change', handler: handleFieldChange, options: false },

    // Selection and drag/drop events
    { event: 'dragstart', handler: handleDragDrop, options: false },
    { event: 'drop', handler: handleDragDrop, options: false },
    { event: 'dragover', handler: (e) => e.preventDefault(), options: false }, // Allow drop
  ];

  listeners.forEach(({ event, handler, options }) => {
    document.addEventListener(event, handler, options);
    interactionListeners.push({ event, handler, options });
  });

  // Setup text selection listener with debouncing
  let selectionTimeout;
  document.addEventListener('selectionchange', () => {
    clearTimeout(selectionTimeout);
    selectionTimeout = setTimeout(handleTextSelection, 300); // Debounce selection events
  });

  // Setup navigation tracking
  setupNavigationTracking();
}

/**
 * Setup navigation tracking for SPA and traditional page changes
 */
function setupNavigationTracking() {
  // Track traditional page navigation
  window.addEventListener('beforeunload', () => {
    if (isCapturing) {
      sendLog({
        type: 'USER_ACTION',
        message: `Leaving page: ${getPageDescription(window.location.href, document.title)}`,
        interaction: {
          type: 'page_unload',
          url: window.location.href,
          title: document.title,
          timestamp: new Date(),
          description: 'Page unload'
        }
      });
    }
  });

  // Track SPA navigation (pushState/replaceState)
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;

  history.pushState = function(...args) {
    originalPushState.apply(this, args);
    setTimeout(handleNavigation, 100); // Allow time for title to update
  };

  history.replaceState = function(...args) {
    originalReplaceState.apply(this, args);
    setTimeout(handleNavigation, 100); // Allow time for title to update
  };

  // Track popstate events (back/forward navigation)
  window.addEventListener('popstate', () => {
    setTimeout(handleNavigation, 100); // Allow time for title to update
  });

  // Track hash changes
  window.addEventListener('hashchange', () => {
    handleNavigation();
  });

  // Periodically check for title changes (for SPAs that update title asynchronously)
  setInterval(() => {
    if (isCapturing) {
      handleNavigation();
    }
  }, 2000);
}

/**
 * Remove all interaction listeners
 */
function removeInteractionListeners() {
  interactionListeners.forEach(({ event, handler, options }) => {
    document.removeEventListener(event, handler, options);
  });
  interactionListeners = [];
}

/**
 * Check if video recording is supported in current environment
 */
function isVideoRecordingSupported() {
  // Check if we're in a secure context (HTTPS or localhost)
  if (!window.isSecureContext) {
    return { supported: false, reason: 'Video recording requires HTTPS or localhost' };
  }

  // Check if getDisplayMedia is supported
  if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
    return { supported: false, reason: 'Screen capture not supported in this browser' };
  }

  // Check if MediaRecorder is supported
  if (!window.MediaRecorder) {
    return { supported: false, reason: 'MediaRecorder not supported in this browser' };
  }

  return { supported: true };
}

/**
 * Start video recording using getDisplayMedia API
 */
async function startVideoRecording() {
  try {
    // Check if video recording is supported
    const supportCheck = isVideoRecordingSupported();
    if (!supportCheck.supported) {
      throw new Error(supportCheck.reason);
    }

    // Show user guidance message
    sendLog({
      type: 'SYSTEM',
      message: 'Starting video recording - please select screen/window to share when prompted'
    });

    // Request screen capture permission with timeout
    const streamPromise = navigator.mediaDevices.getDisplayMedia({
      video: {
        mediaSource: 'screen',
        width: { ideal: 1920, max: 1920 },
        height: { ideal: 1080, max: 1080 },
        frameRate: { ideal: 30, max: 60 }
      },
      audio: false // We'll focus on video for now
    });

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Screen sharing request timed out')), 30000);
    });

    const stream = await Promise.race([streamPromise, timeoutPromise]);

    // Check if user cancelled the screen share
    if (!stream) {
      throw new Error('User cancelled screen sharing');
    }

    // Initialize MediaRecorder with optimal settings
    const options = {
      mimeType: 'video/webm;codecs=vp9,opus',
      videoBitsPerSecond: 2500000 // 2.5 Mbps for good quality
    };

    // Fallback to vp8 if vp9 is not supported
    if (!MediaRecorder.isTypeSupported(options.mimeType)) {
      options.mimeType = 'video/webm;codecs=vp8,opus';
      if (!MediaRecorder.isTypeSupported(options.mimeType)) {
        options.mimeType = 'video/webm';
      }
    }

    mediaRecorder = new MediaRecorder(stream, options);
    recordedChunks = [];

    // Handle data availability
    mediaRecorder.ondataavailable = (event) => {
      if (event.data && event.data.size > 0) {
        recordedChunks.push(event.data);
        console.log('BugReplay: Video chunk recorded, size:', event.data.size);
      }
    };

    // Handle recording stop
    mediaRecorder.onstop = () => {
      console.log('BugReplay: MediaRecorder stopped, processing video...');
      processVideoRecording();
    };

    // Handle errors
    mediaRecorder.onerror = (event) => {
      console.error('BugReplay: MediaRecorder error:', event.error);
      sendLog({
        type: 'ERROR',
        message: `Video recording error: ${event.error.message}`
      });
    };

    // Handle stream end (user stops sharing)
    stream.getVideoTracks()[0].addEventListener('ended', () => {
      console.log('BugReplay: Screen sharing ended by user');
      if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
      }
      sendLog({
        type: 'SYSTEM',
        message: 'Screen sharing ended by user'
      });
    });

    // Start recording
    mediaRecorder.start(1000); // Collect data every second

    sendLog({
      type: 'SYSTEM',
      message: 'Video recording started - screen capture active'
    });

    console.log('BugReplay: Video recording started successfully');
    return { success: true, message: 'Video recording started' };

  } catch (error) {
    console.error('BugReplay: Failed to start video recording:', error);

    let errorMessage = error.message;
    let userGuidance = '';

    if (error.name === 'NotAllowedError') {
      errorMessage = 'Screen capture permission denied by user';
      userGuidance = 'Please allow screen sharing when prompted to enable video recording.';
    } else if (error.name === 'NotSupportedError') {
      errorMessage = 'Screen capture not supported in this browser';
      userGuidance = 'Try using Chrome, Firefox, or Edge for video recording support.';
    } else if (error.name === 'AbortError') {
      errorMessage = 'Screen capture was cancelled by user';
      userGuidance = 'Click "Start Video" again and select a screen/window to share.';
    } else if (error.message.includes('HTTPS')) {
      userGuidance = 'Video recording requires HTTPS. Try accessing the site with https:// or use localhost.';
    } else if (error.message.includes('timed out')) {
      userGuidance = 'Screen sharing request timed out. Please try again and respond to the permission prompt quickly.';
    }

    const fullMessage = userGuidance ? `${errorMessage}. ${userGuidance}` : errorMessage;

    sendLog({
      type: 'ERROR',
      message: `Failed to start video recording: ${fullMessage}`
    });

    return { success: false, error: errorMessage, guidance: userGuidance };
  }
}

/**
 * Stop video recording
 */
async function stopVideoRecording() {
  try {
    if (mediaRecorder && mediaRecorder.state !== 'inactive') {
      console.log('BugReplay: Stopping video recording...');

      // Stop the MediaRecorder
      mediaRecorder.stop();

      // Stop all tracks to release screen capture
      if (mediaRecorder.stream) {
        mediaRecorder.stream.getTracks().forEach(track => {
          track.stop();
          console.log('BugReplay: Stopped video track:', track.kind);
        });
      }

      sendLog({
        type: 'SYSTEM',
        message: 'Video recording stopped and processing...'
      });

      return { success: true, message: 'Video recording stopped' };
    } else {
      console.warn('BugReplay: No active video recording to stop');
      return { success: false, error: 'No active recording' };
    }
  } catch (error) {
    console.error('BugReplay: Error stopping video recording:', error);
    sendLog({
      type: 'ERROR',
      message: `Error stopping video recording: ${error.message}`
    });
    return { success: false, error: error.message };
  }
}

/**
 * Process recorded video data
 */
async function processVideoRecording() {
  try {
    if (recordedChunks.length === 0) {
      console.warn('BugReplay: No video data recorded');
      sendLog({
        type: 'SYSTEM',
        message: 'No video data was recorded'
      });
      return;
    }

    console.log('BugReplay: Processing video data, chunks:', recordedChunks.length);

    // Determine the correct MIME type based on what was actually recorded
    let mimeType = 'video/webm';
    if (mediaRecorder && mediaRecorder.mimeType) {
      mimeType = mediaRecorder.mimeType;
    }

    // Create blob from recorded chunks
    const videoBlob = new Blob(recordedChunks, { type: mimeType });

    console.log('BugReplay: Video blob created, size:', videoBlob.size, 'type:', mimeType);

    if (videoBlob.size === 0) {
      throw new Error('Video blob is empty');
    }

    // Create object URL for the video
    const videoUrl = URL.createObjectURL(videoBlob);

    // Save video file locally
    await saveVideoFile(videoBlob, mimeType);

    // Send video data to background script
    chrome.runtime.sendMessage({
      type: 'VIDEO_RECORDING_DATA',
      videoData: {
        videoUrl: videoUrl,
        videoSize: videoBlob.size,
        mimeType: mimeType,
        duration: calculateVideoDuration(),
        recordedAt: new Date().toISOString()
      }
    });

    sendLog({
      type: 'SYSTEM',
      message: `Video recording saved successfully (${formatFileSize(videoBlob.size)})`
    });

    console.log('BugReplay: Video processing completed successfully');

  } catch (error) {
    console.error('BugReplay: Error processing video recording:', error);
    sendLog({
      type: 'ERROR',
      message: `Error processing video recording: ${error.message}`
    });
  }
}

/**
 * Calculate approximate video duration
 */
function calculateVideoDuration() {
  // This is an approximation based on recording chunks
  // Each chunk represents roughly 1 second of recording
  return recordedChunks.length;
}

/**
 * Save video file to local storage (downloads)
 */
async function saveVideoFile(videoBlob, mimeType = 'video/webm') {
  try {
    // Determine file extension based on MIME type
    let extension = 'webm';
    if (mimeType.includes('mp4')) {
      extension = 'mp4';
    } else if (mimeType.includes('webm')) {
      extension = 'webm';
    }

    // Generate filename with timestamp and session ID
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const sessionId = currentSessionId || 'unknown';
    const fileName = `bugreplay-video-${sessionId}-${timestamp}.${extension}`;

    console.log('BugReplay: Saving video file:', fileName, 'Size:', videoBlob.size);

    // Create download link
    const url = URL.createObjectURL(videoBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.style.display = 'none';

    // Add to DOM, trigger download, then remove
    document.body.appendChild(link);

    // Use a small delay to ensure the link is properly added to DOM
    await new Promise(resolve => setTimeout(resolve, 100));

    link.click();

    // Clean up after a delay to ensure download starts
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      console.log('BugReplay: Video download cleanup completed');
    }, 1000);

    sendLog({
      type: 'SYSTEM',
      message: `Video saved as ${fileName} (${formatFileSize(videoBlob.size)})`
    });

    console.log('BugReplay: Video file download initiated successfully');

  } catch (error) {
    console.error('BugReplay: Error saving video file:', error);
    sendLog({
      type: 'ERROR',
      message: `Error saving video file: ${error.message}`
    });
  }
}

/**
 * Format file size for display
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Start capturing user actions and logs with enhanced tracking
 */
function startCapturing() {
  if (isCapturing) return;
  isCapturing = true;
  currentSessionId = arguments[0]?.sessionId || null;

  // Initialize current page state
  currentUrl = window.location.href;
  currentTitle = document.title;

  overrideConsole();
  overrideFetch();
  setupInteractionListeners();
  initializeMutationObserver();

  // Log initial page state
  sendLog({
    type: 'SYSTEM',
    message: `Started recording on page: ${getPageDescription(currentUrl, currentTitle)}`,
    interaction: {
      type: 'recording_start',
      url: currentUrl,
      title: currentTitle,
      timestamp: new Date(),
      description: 'Recording session started'
    }
  });
}

/**
 * Stop capturing user actions and logs
 */
function stopCapturing() {
  if (!isCapturing) return;
  isCapturing = false;
  currentSessionId = null;

  restoreConsole();
  restoreFetch();
  removeInteractionListeners();

  if (mutationObserver) {
    mutationObserver.disconnect();
    mutationObserver = null;
  }

  sendLog({ type: 'SYSTEM', message: 'Content script stopped comprehensive capturing.' });
}

if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'PING') {
      sendResponse({ pong: true, ready: true });
      return false;
    } else if (request.type === 'START_RECORDING_CONTENT') {
      startCapturing({ sessionId: request.sessionId });
      sendResponse({
        success: true,
        message: 'Enhanced recording started in content script.',
        sessionId: request.sessionId
      });
      return false;
    } else if (request.type === 'STOP_RECORDING_CONTENT') {
      stopCapturing();
      sendResponse({
        success: true,
        message: 'Enhanced recording stopped in content script.',
        sessionId: request.sessionId
      });
      return false;
    } else if (request.type === 'START_VIDEO_RECORDING') {
      startVideoRecording().then(result => {
        sendResponse(result);
      }).catch(error => {
        sendResponse({ success: false, error: error.message });
      });
      return true; // Async response
    } else if (request.type === 'STOP_VIDEO_RECORDING') {
      stopVideoRecording().then(result => {
        sendResponse(result);
      }).catch(error => {
        sendResponse({ success: false, error: error.message });
      });
      return true; // Async response
    }
    return false; // Synchronous response
  });
}

// Initial overrides if the extension starts in a "recording" state (not typical for manual start)
// or handle state from storage if implementing persistence.
// For now, relies on popup message to start.
console.log('BugReplay content script loaded.');