/**
 * Storage Services Index
 * 
 * This file exports all storage-related services and utilities
 * for the BugReplay extension.
 */

export { 
  BugReplayDatabase, 
  DatabaseManager, 
  db, 
  DATABASE_NAME, 
  DATABASE_VERSION 
} from './DatabaseSchema.js';

export { SessionStorageService } from './SessionStorageService.js';

export { 
  MigrationService, 
  MigrationStatus 
} from './MigrationService.js';

/**
 * Storage service factory
 * Creates and initializes the appropriate storage service
 */
export class StorageServiceFactory {
  static async createStorageService() {
    const service = new SessionStorageService();
    await service.initialize();
    return service;
  }

  static async createMigrationService() {
    return new MigrationService();
  }
}

/**
 * Global storage service instance
 * This will be initialized once and reused throughout the application
 */
let globalStorageService = null;

/**
 * Get the global storage service instance
 * @returns {Promise<SessionStorageService>} Storage service instance
 */
export async function getStorageService() {
  if (!globalStorageService) {
    globalStorageService = await StorageServiceFactory.createStorageService();
  }
  return globalStorageService;
}

/**
 * Initialize storage system with migration if needed
 * @param {Function} progressCallback - Migration progress callback
 * @returns {Promise<Object>} Initialization result
 */
export async function initializeStorage(progressCallback = null) {
  try {
    console.log('Initializing BugReplay storage system...');

    // Check if migration is needed
    const migrationService = new MigrationService();
    const needsMigration = await migrationService.isMigrationNeeded();

    if (needsMigration) {
      console.log('Migration needed, starting migration process...');
      
      if (progressCallback) {
        progressCallback({ type: 'migration_start' });
      }

      const migrationResult = await migrationService.migrate((progress) => {
        if (progressCallback) {
          progressCallback({ type: 'migration_progress', ...progress });
        }
      });

      if (progressCallback) {
        progressCallback({ 
          type: 'migration_complete', 
          success: migrationResult.status === 'completed',
          result: migrationResult.toJSON()
        });
      }

      if (migrationResult.status === 'failed') {
        console.error('Migration failed:', migrationResult.errors);
        // Continue with Chrome storage fallback
      }
    }

    // Initialize storage service
    const storageService = await getStorageService();
    
    console.log('Storage system initialized successfully');
    return {
      success: true,
      migrationPerformed: needsMigration,
      usingIndexedDB: !storageService.fallbackToChrome
    };

  } catch (error) {
    console.error('Failed to initialize storage system:', error);
    return {
      success: false,
      error: error.message,
      migrationPerformed: false,
      usingIndexedDB: false
    };
  }
}

/**
 * Storage utilities
 */
export const StorageUtils = {
  /**
   * Format bytes to human readable string
   * @param {number} bytes - Bytes to format
   * @returns {string} Formatted string
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * Format duration to human readable string
   * @param {number} duration - Duration in milliseconds
   * @returns {string} Formatted string
   */
  formatDuration(duration) {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  },

  /**
   * Validate session data structure
   * @param {Object} sessionData - Session data to validate
   * @returns {Object} Validation result
   */
  validateSessionData(sessionData) {
    const errors = [];

    if (!sessionData || typeof sessionData !== 'object') {
      return { valid: false, errors: ['Invalid session data format'] };
    }

    if (!sessionData.id || typeof sessionData.id !== 'string') {
      errors.push('Missing or invalid session ID');
    }

    if (!sessionData.startTime) {
      errors.push('Missing start time');
    }

    if (!sessionData.url || typeof sessionData.url !== 'string') {
      errors.push('Missing or invalid URL');
    }

    if (!Array.isArray(sessionData.logs)) {
      errors.push('Missing or invalid logs array');
    }

    if (!Array.isArray(sessionData.screenshots)) {
      errors.push('Missing or invalid screenshots array');
    }

    const hasValidStructure = sessionData.hasOwnProperty('tabId') &&
                             sessionData.hasOwnProperty('title') &&
                             sessionData.hasOwnProperty('state');

    if (!hasValidStructure) {
      errors.push('Missing required session structure fields');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Generate unique session ID
   * @returns {string} Unique session ID
   */
  generateSessionId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `session_${timestamp}_${random}`;
  },

  /**
   * Check if storage quota is approaching limit
   * @param {Object} usage - Storage usage data
   * @param {number} threshold - Warning threshold (0-1)
   * @returns {boolean} True if approaching limit
   */
  isApproachingQuota(usage, threshold = 0.8) {
    return usage.percentage >= (threshold * 100);
  },

  /**
   * Get storage health status
   * @param {Object} usage - Storage usage data
   * @returns {Object} Health status
   */
  getStorageHealth(usage) {
    const percentage = usage.percentage;
    
    let status = 'good';
    let message = 'Storage usage is healthy';
    let color = 'green';

    if (percentage >= 90) {
      status = 'critical';
      message = 'Storage is nearly full. Please delete old sessions.';
      color = 'red';
    } else if (percentage >= 80) {
      status = 'warning';
      message = 'Storage usage is high. Consider cleaning up old sessions.';
      color = 'yellow';
    } else if (percentage >= 60) {
      status = 'moderate';
      message = 'Storage usage is moderate.';
      color = 'orange';
    }

    return {
      status,
      message,
      color,
      percentage,
      shouldCleanup: percentage >= 80,
      shouldWarn: percentage >= 60
    };
  }
};
