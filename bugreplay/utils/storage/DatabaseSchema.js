/**
 * IndexedDB Database Schema for BugReplay Extension
 * 
 * This file defines the database structure, versioning, and migration logic
 * for the BugReplay session storage system using Dexie.js
 */

import <PERSON>ie from 'dexie';

/**
 * Database version history and schema definitions
 */
export const DATABASE_NAME = 'BugReplayDB';
export const DATABASE_VERSION = 1;

/**
 * Session data structure interface
 * @typedef {Object} SessionData
 * @property {string} id - Unique session identifier
 * @property {string} title - Session title
 * @property {string} url - URL where session was recorded
 * @property {Date} startTime - Recording start time
 * @property {Date} endTime - Recording end time
 * @property {Date} completedAt - Session completion timestamp
 * @property {number} duration - Recording duration in milliseconds
 * @property {number} size - Session data size in bytes
 * @property {string} status - Session status (completed, processing, error, imported)
 * @property {number} screenshotCount - Number of screenshots
 * @property {number} logCount - Number of log entries
 * @property {number} networkRequestCount - Number of network requests
 * @property {Array} logs - Session log entries
 * @property {Array} screenshots - Screenshot data
 * @property {Object} harData - HAR file data for network requests
 * @property {string} tabId - Browser tab ID
 * @property {string} state - Recording state
 * @property {boolean} imported - Whether session was imported
 * @property {Date} importedAt - Import timestamp (if imported)
 * @property {Object} metadata - Additional session metadata
 */

/**
 * Storage usage statistics structure
 * @typedef {Object} StorageUsage
 * @property {number} totalSize - Total database size in bytes
 * @property {number} sessionCount - Number of stored sessions
 * @property {number} averageSessionSize - Average session size
 * @property {Date} lastCalculated - Last calculation timestamp
 * @property {Object} breakdown - Size breakdown by data type
 */

/**
 * BugReplay Database Class
 * Extends Dexie to provide typed access to our database schema
 */
export class BugReplayDatabase extends Dexie {
  constructor() {
    super(DATABASE_NAME);
    
    // Define database schema
    this.version(1).stores({
      // Sessions table - stores complete session data
      sessions: '++id, title, url, startTime, endTime, completedAt, status, size, imported',
      
      // Storage metadata table - for caching and analytics
      storageMetadata: '++id, type, data, lastUpdated',
      
      // Settings table - for extension configuration
      settings: '++id, key, value, lastModified'
    });

    // Define table types for TypeScript-like intellisense
    this.sessions = this.table('sessions');
    this.storageMetadata = this.table('storageMetadata');
    this.settings = this.table('settings');

    // Add hooks for automatic data management
    this.sessions.hook('creating', this._onSessionCreating.bind(this));
    this.sessions.hook('updating', this._onSessionUpdating.bind(this));
    this.sessions.hook('deleting', this._onSessionDeleting.bind(this));
  }

  /**
   * Hook called when creating a new session
   * Automatically sets creation metadata
   */
  _onSessionCreating(primKey, obj, trans) {
    if (!obj.completedAt) {
      obj.completedAt = new Date();
    }
    if (!obj.size) {
      obj.size = this._calculateSessionSize(obj);
    }
  }

  /**
   * Hook called when updating a session
   * Updates modification metadata
   */
  _onSessionUpdating(modifications, primKey, obj, trans) {
    if (modifications.hasOwnProperty('logs') || 
        modifications.hasOwnProperty('screenshots') || 
        modifications.hasOwnProperty('harData')) {
      modifications.size = this._calculateSessionSize({...obj, ...modifications});
    }
  }

  /**
   * Hook called when deleting a session
   * Cleanup related data and update cache
   */
  _onSessionDeleting(primKey, obj, trans) {
    // Invalidate storage usage cache when sessions are deleted
    this._invalidateStorageCache();
  }

  /**
   * Calculate session data size
   * @param {Object} sessionData - Session data object
   * @returns {number} Size in bytes
   */
  _calculateSessionSize(sessionData) {
    let size = 0;
    
    try {
      // Calculate base session metadata size
      const baseData = {
        id: sessionData.id,
        title: sessionData.title,
        url: sessionData.url,
        startTime: sessionData.startTime,
        endTime: sessionData.endTime,
        status: sessionData.status
      };
      size += JSON.stringify(baseData).length;

      // Add logs size
      if (sessionData.logs) {
        size += JSON.stringify(sessionData.logs).length;
      }

      // Add screenshots size
      if (sessionData.screenshots) {
        size += sessionData.screenshots.reduce((acc, screenshot) => {
          if (typeof screenshot === 'string') {
            return acc + screenshot.length;
          } else if (screenshot && screenshot.dataUrl) {
            return acc + screenshot.dataUrl.length;
          }
          return acc;
        }, 0);
      }

      // Add HAR data size
      if (sessionData.harData) {
        size += JSON.stringify(sessionData.harData).length;
      }

      // Add metadata size
      if (sessionData.metadata) {
        size += JSON.stringify(sessionData.metadata).length;
      }
    } catch (error) {
      console.warn('Error calculating session size:', error);
    }

    return size;
  }

  /**
   * Invalidate storage usage cache
   */
  _invalidateStorageCache() {
    this.storageMetadata.where('type').equals('usage_cache').delete().catch(err => {
      console.warn('Error invalidating storage cache:', err);
    });
  }

  /**
   * Get database size estimation
   * @returns {Promise<number>} Estimated database size in bytes
   */
  async getDatabaseSize() {
    try {
      // Get all sessions and calculate total size
      const sessions = await this.sessions.toArray();
      const totalSize = sessions.reduce((acc, session) => acc + (session.size || 0), 0);
      
      // Add overhead for metadata and indexes (estimated 10%)
      return Math.round(totalSize * 1.1);
    } catch (error) {
      console.error('Error calculating database size:', error);
      return 0;
    }
  }

  /**
   * Cleanup old or corrupted data
   * @param {Object} options - Cleanup options
   * @returns {Promise<Object>} Cleanup results
   */
  async cleanup(options = {}) {
    const {
      maxSessions = 100,
      maxAge = 30 * 24 * 60 * 60 * 1000, // 30 days
      removeCorrupted = true
    } = options;

    const results = {
      sessionsRemoved: 0,
      corruptedRemoved: 0,
      spaceFreed: 0
    };

    try {
      await this.transaction('rw', this.sessions, async () => {
        // Remove old sessions
        const cutoffDate = new Date(Date.now() - maxAge);
        const oldSessions = await this.sessions
          .where('completedAt')
          .below(cutoffDate)
          .toArray();

        if (oldSessions.length > 0) {
          const spaceFreed = oldSessions.reduce((acc, session) => acc + (session.size || 0), 0);
          await this.sessions.where('completedAt').below(cutoffDate).delete();
          results.sessionsRemoved += oldSessions.length;
          results.spaceFreed += spaceFreed;
        }

        // Remove excess sessions (keep most recent)
        const totalSessions = await this.sessions.count();
        if (totalSessions > maxSessions) {
          const excessCount = totalSessions - maxSessions;
          const oldestSessions = await this.sessions
            .orderBy('completedAt')
            .limit(excessCount)
            .toArray();

          const spaceFreed = oldestSessions.reduce((acc, session) => acc + (session.size || 0), 0);
          await this.sessions
            .orderBy('completedAt')
            .limit(excessCount)
            .delete();
          
          results.sessionsRemoved += excessCount;
          results.spaceFreed += spaceFreed;
        }

        // Remove corrupted sessions if requested
        if (removeCorrupted) {
          const corruptedSessions = await this.sessions
            .filter(session => !session.id || !session.startTime)
            .toArray();

          if (corruptedSessions.length > 0) {
            await this.sessions
              .filter(session => !session.id || !session.startTime)
              .delete();
            results.corruptedRemoved = corruptedSessions.length;
          }
        }
      });

      // Clear storage cache after cleanup
      this._invalidateStorageCache();

      console.log('Database cleanup completed:', results);
      return results;
    } catch (error) {
      console.error('Error during database cleanup:', error);
      throw error;
    }
  }
}

/**
 * Create and export database instance
 */
export const db = new BugReplayDatabase();

/**
 * Database initialization and migration utilities
 */
export class DatabaseManager {
  /**
   * Initialize database and handle migrations
   * @returns {Promise<boolean>} Success status
   */
  static async initialize() {
    try {
      await db.open();
      console.log('BugReplay database initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize database:', error);
      return false;
    }
  }

  /**
   * Check if database is available and accessible
   * @returns {Promise<boolean>} Availability status
   */
  static async isAvailable() {
    try {
      await db.sessions.limit(1).toArray();
      return true;
    } catch (error) {
      console.warn('Database not available:', error);
      return false;
    }
  }

  /**
   * Get database information and statistics
   * @returns {Promise<Object>} Database info
   */
  static async getInfo() {
    try {
      const sessionCount = await db.sessions.count();
      const totalSize = await db.getDatabaseSize();
      const oldestSession = await db.sessions.orderBy('completedAt').first();
      const newestSession = await db.sessions.orderBy('completedAt').last();

      return {
        name: DATABASE_NAME,
        version: DATABASE_VERSION,
        sessionCount,
        totalSize,
        oldestSession: oldestSession?.completedAt,
        newestSession: newestSession?.completedAt,
        isOpen: db.isOpen()
      };
    } catch (error) {
      console.error('Error getting database info:', error);
      return null;
    }
  }
}
