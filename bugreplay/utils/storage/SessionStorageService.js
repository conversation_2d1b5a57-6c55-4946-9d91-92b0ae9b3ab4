/**
 * Session Storage Service for BugReplay Extension
 * 
 * This service provides a high-level interface for managing session data
 * using IndexedDB with caching, error handling, and performance optimizations.
 */

import { db, DatabaseManager } from './DatabaseSchema.js';

/**
 * Storage service configuration
 */
const CONFIG = {
  CACHE_TTL: 5 * 60 * 1000, // 5 minutes cache TTL
  MAX_CACHE_SIZE: 50, // Maximum cached sessions
  BATCH_SIZE: 20, // Pagination batch size
  STORAGE_QUOTA: 100 * 1024 * 1024, // 100MB estimated quota
  CLEANUP_THRESHOLD: 0.8 // Cleanup when 80% full
};

/**
 * In-memory cache for frequently accessed data
 */
class SessionCache {
  constructor() {
    this.sessions = new Map();
    this.metadata = new Map();
    this.lastCleanup = Date.now();
  }

  get(key) {
    const item = this.sessions.get(key);
    if (item && Date.now() - item.timestamp < CONFIG.CACHE_TTL) {
      return item.data;
    }
    this.sessions.delete(key);
    return null;
  }

  set(key, data) {
    // Cleanup old entries if cache is full
    if (this.sessions.size >= CONFIG.MAX_CACHE_SIZE) {
      this.cleanup();
    }
    
    this.sessions.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  delete(key) {
    this.sessions.delete(key);
  }

  clear() {
    this.sessions.clear();
    this.metadata.clear();
  }

  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.sessions.entries()) {
      if (now - item.timestamp > CONFIG.CACHE_TTL) {
        this.sessions.delete(key);
      }
    }
  }

  getMetadata(key) {
    const item = this.metadata.get(key);
    if (item && Date.now() - item.timestamp < CONFIG.CACHE_TTL) {
      return item.data;
    }
    this.metadata.delete(key);
    return null;
  }

  setMetadata(key, data) {
    this.metadata.set(key, {
      data,
      timestamp: Date.now()
    });
  }
}

/**
 * Main Session Storage Service
 */
export class SessionStorageService {
  constructor() {
    this.cache = new SessionCache();
    this.initialized = false;
    this.fallbackToChrome = false;
  }

  /**
   * Initialize the storage service
   * @returns {Promise<boolean>} Success status
   */
  async initialize() {
    try {
      const isAvailable = await DatabaseManager.isAvailable();
      if (!isAvailable) {
        console.warn('IndexedDB not available, falling back to Chrome storage');
        this.fallbackToChrome = true;
        return true;
      }

      await DatabaseManager.initialize();
      this.initialized = true;
      console.log('SessionStorageService initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize SessionStorageService:', error);
      this.fallbackToChrome = true;
      return false;
    }
  }

  /**
   * Save a completed session
   * @param {Object} sessionData - Session data to save
   * @returns {Promise<Object>} Save result
   */
  async saveSession(sessionData) {
    try {
      if (this.fallbackToChrome) {
        return await this._saveSessionChrome(sessionData);
      }

      // Check storage usage before saving
      const usage = await this.getStorageUsage();
      if (usage.percentage > CONFIG.CLEANUP_THRESHOLD * 100) {
        console.log('Storage approaching limit, cleaning up...');
        await this.cleanup();
      }

      // Prepare session data
      const session = {
        ...sessionData,
        completedAt: sessionData.completedAt || new Date(),
        size: sessionData.size || this._calculateSessionSize(sessionData)
      };

      // Save to database
      const id = await db.sessions.add(session);
      
      // Update cache
      this.cache.set(session.id, session);
      this.cache.setMetadata('storage_usage', null); // Invalidate usage cache

      console.log('Session saved successfully:', session.id);
      return {
        success: true,
        sessionId: session.id,
        size: session.size
      };
    } catch (error) {
      console.error('Error saving session:', error);
      
      // Handle quota exceeded
      if (error.name === 'QuotaExceededError') {
        await this.handleQuotaExceeded();
        // Retry once after cleanup
        try {
          const id = await db.sessions.add(sessionData);
          return { success: true, sessionId: sessionData.id };
        } catch (retryError) {
          return { success: false, error: 'Storage quota exceeded' };
        }
      }
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Get all completed sessions with pagination
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Sessions and metadata
   */
  async getSessions(options = {}) {
    try {
      if (this.fallbackToChrome) {
        return await this._getSessionsChrome(options);
      }

      const {
        offset = 0,
        limit = CONFIG.BATCH_SIZE,
        sortBy = 'completedAt',
        sortOrder = 'desc',
        search = '',
        status = null
      } = options;

      // Build query
      let query = db.sessions.orderBy(sortBy);
      
      if (sortOrder === 'desc') {
        query = query.reverse();
      }

      // Apply filters
      if (status) {
        query = query.filter(session => session.status === status);
      }

      if (search) {
        const searchLower = search.toLowerCase();
        query = query.filter(session => 
          session.title?.toLowerCase().includes(searchLower) ||
          session.url?.toLowerCase().includes(searchLower) ||
          session.id.toLowerCase().includes(searchLower)
        );
      }

      // Get paginated results
      const sessions = await query.offset(offset).limit(limit).toArray();
      const totalCount = await query.count();

      // Convert dates from strings if needed
      const processedSessions = sessions.map(session => ({
        ...session,
        startTime: new Date(session.startTime),
        endTime: session.endTime ? new Date(session.endTime) : null,
        completedAt: new Date(session.completedAt)
      }));

      return {
        success: true,
        sessions: processedSessions,
        totalCount,
        hasMore: offset + limit < totalCount
      };
    } catch (error) {
      console.error('Error getting sessions:', error);
      return { success: false, error: error.message, sessions: [] };
    }
  }

  /**
   * Get a specific session by ID
   * @param {string} sessionId - Session ID
   * @returns {Promise<Object>} Session data
   */
  async getSession(sessionId) {
    try {
      if (this.fallbackToChrome) {
        return await this._getSessionChrome(sessionId);
      }

      // Check cache first
      const cached = this.cache.get(sessionId);
      if (cached) {
        return { success: true, session: cached };
      }

      // Get from database
      const session = await db.sessions.where('id').equals(sessionId).first();
      
      if (!session) {
        return { success: false, error: 'Session not found' };
      }

      // Convert dates and cache result
      const processedSession = {
        ...session,
        startTime: new Date(session.startTime),
        endTime: session.endTime ? new Date(session.endTime) : null,
        completedAt: new Date(session.completedAt)
      };

      this.cache.set(sessionId, processedSession);
      
      return { success: true, session: processedSession };
    } catch (error) {
      console.error('Error getting session:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete a session
   * @param {string} sessionId - Session ID to delete
   * @returns {Promise<Object>} Delete result
   */
  async deleteSession(sessionId) {
    try {
      if (this.fallbackToChrome) {
        return await this._deleteSessionChrome(sessionId);
      }

      const deletedCount = await db.sessions.where('id').equals(sessionId).delete();
      
      if (deletedCount === 0) {
        return { success: false, error: 'Session not found' };
      }

      // Remove from cache
      this.cache.delete(sessionId);
      this.cache.setMetadata('storage_usage', null); // Invalidate usage cache

      console.log('Session deleted successfully:', sessionId);
      return { success: true };
    } catch (error) {
      console.error('Error deleting session:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete multiple sessions
   * @param {string[]} sessionIds - Array of session IDs
   * @returns {Promise<Object>} Delete result
   */
  async deleteSessions(sessionIds) {
    try {
      if (this.fallbackToChrome) {
        return await this._deleteSessionsChrome(sessionIds);
      }

      const deletedCount = await db.sessions.where('id').anyOf(sessionIds).delete();

      // Remove from cache
      sessionIds.forEach(id => this.cache.delete(id));
      this.cache.setMetadata('storage_usage', null); // Invalidate usage cache

      console.log(`Deleted ${deletedCount} sessions successfully`);
      return { success: true, deletedCount };
    } catch (error) {
      console.error('Error deleting sessions:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get storage usage statistics
   * @returns {Promise<Object>} Storage usage info
   */
  async getStorageUsage() {
    try {
      if (this.fallbackToChrome) {
        return await this._getStorageUsageChrome();
      }

      // Check cache first
      const cached = this.cache.getMetadata('storage_usage');
      if (cached) {
        return { success: true, usage: cached };
      }

      // Calculate usage
      const sessionCount = await db.sessions.count();
      const totalSize = await db.getDatabaseSize();
      
      const sessions = await db.sessions.toArray();
      const totalSessionSize = sessions.reduce((acc, session) => acc + (session.size || 0), 0);
      const averageSessionSize = sessionCount > 0 ? Math.round(totalSessionSize / sessionCount) : 0;

      const usage = {
        used: totalSize,
        quota: CONFIG.STORAGE_QUOTA,
        available: CONFIG.STORAGE_QUOTA - totalSize,
        percentage: Math.round((totalSize / CONFIG.STORAGE_QUOTA) * 100),
        sessionCount,
        averageSessionSize,
        totalSessionSize
      };

      // Cache the result
      this.cache.setMetadata('storage_usage', usage);

      return { success: true, usage };
    } catch (error) {
      console.error('Error getting storage usage:', error);
      return {
        success: false,
        error: error.message,
        usage: {
          used: 0,
          quota: CONFIG.STORAGE_QUOTA,
          available: CONFIG.STORAGE_QUOTA,
          percentage: 0,
          sessionCount: 0,
          averageSessionSize: 0,
          totalSessionSize: 0
        }
      };
    }
  }

  /**
   * Import a session from external data
   * @param {Object} sessionData - Session data to import
   * @returns {Promise<Object>} Import result
   */
  async importSession(sessionData) {
    try {
      if (this.fallbackToChrome) {
        return await this._importSessionChrome(sessionData);
      }

      // Check for ID conflicts
      const existingSession = await db.sessions.where('id').equals(sessionData.id).first();
      
      if (existingSession) {
        // Generate new ID with timestamp suffix
        const timestamp = Date.now();
        sessionData.id = `${sessionData.id}_imported_${timestamp}`;
      }

      // Prepare imported session
      const importedSession = {
        ...sessionData,
        startTime: new Date(sessionData.startTime),
        endTime: sessionData.endTime ? new Date(sessionData.endTime) : null,
        completedAt: new Date(),
        status: 'imported',
        imported: true,
        importedAt: new Date(),
        size: this._calculateSessionSize(sessionData)
      };

      // Save to database
      await db.sessions.add(importedSession);

      console.log('Session imported successfully:', importedSession.id);
      return {
        success: true,
        sessionId: importedSession.id,
        message: `Session "${sessionData.title || sessionData.id}" imported successfully`
      };
    } catch (error) {
      console.error('Error importing session:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Cleanup old sessions and optimize storage
   * @param {Object} options - Cleanup options
   * @returns {Promise<Object>} Cleanup result
   */
  async cleanup(options = {}) {
    try {
      if (this.fallbackToChrome) {
        return await this._cleanupChrome(options);
      }

      const result = await db.cleanup(options);
      
      // Clear cache after cleanup
      this.cache.clear();

      console.log('Storage cleanup completed:', result);
      return { success: true, ...result };
    } catch (error) {
      console.error('Error during cleanup:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle storage quota exceeded
   */
  async handleQuotaExceeded() {
    console.log('Handling storage quota exceeded...');
    
    try {
      // Aggressive cleanup
      await this.cleanup({
        maxSessions: 20,
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        removeCorrupted: true
      });
    } catch (error) {
      console.error('Error handling quota exceeded:', error);
    }
  }

  /**
   * Calculate session data size
   * @param {Object} sessionData - Session data
   * @returns {number} Size in bytes
   */
  _calculateSessionSize(sessionData) {
    // This method is implemented in DatabaseSchema.js
    // We'll delegate to the database's calculation method
    return db._calculateSessionSize(sessionData);
  }

  /**
   * Chrome storage fallback methods
   * These methods maintain compatibility with the existing chrome.storage.local API
   */

  async _saveSessionChrome(sessionData) {
    try {
      const session = {
        ...sessionData,
        completedAt: sessionData.completedAt || new Date(),
        size: sessionData.size || this._calculateSessionSize(sessionData)
      };

      // Get existing sessions list
      const result = await chrome.storage.local.get(['completedSessions']);
      const completedSessions = result.completedSessions || [];

      // Add new session to the list
      completedSessions.push({
        id: session.id,
        title: session.title,
        url: session.url,
        startTime: session.startTime,
        endTime: session.endTime,
        completedAt: session.completedAt,
        duration: session.endTime ? session.endTime - session.startTime : 0,
        size: session.size,
        status: session.status,
        screenshotCount: session.screenshots?.length || 0,
        logCount: session.logs?.length || 0,
        networkRequestCount: session.harData?.log?.entries?.length || 0
      });

      // Save both the session data and the updated list
      await chrome.storage.local.set({
        [`completed_session_${session.id}`]: session,
        'completedSessions': completedSessions
      });

      return { success: true, sessionId: session.id, size: session.size };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async _getSessionsChrome(options = {}) {
    try {
      const result = await chrome.storage.local.get(['completedSessions']);
      let sessions = result.completedSessions || [];

      // Apply search filter
      if (options.search) {
        const searchLower = options.search.toLowerCase();
        sessions = sessions.filter(session =>
          session.title?.toLowerCase().includes(searchLower) ||
          session.url?.toLowerCase().includes(searchLower) ||
          session.id.toLowerCase().includes(searchLower)
        );
      }

      // Apply status filter
      if (options.status) {
        sessions = sessions.filter(session => session.status === options.status);
      }

      // Apply sorting
      const sortBy = options.sortBy || 'completedAt';
      const sortOrder = options.sortOrder || 'desc';

      sessions.sort((a, b) => {
        let aVal = a[sortBy];
        let bVal = b[sortBy];

        if (sortBy.includes('Time') || sortBy.includes('At')) {
          aVal = new Date(aVal);
          bVal = new Date(bVal);
        }

        if (sortOrder === 'asc') {
          return aVal > bVal ? 1 : -1;
        } else {
          return aVal < bVal ? 1 : -1;
        }
      });

      // Apply pagination
      const offset = options.offset || 0;
      const limit = options.limit || CONFIG.BATCH_SIZE;
      const paginatedSessions = sessions.slice(offset, offset + limit);

      return {
        success: true,
        sessions: paginatedSessions,
        totalCount: sessions.length,
        hasMore: offset + limit < sessions.length
      };
    } catch (error) {
      return { success: false, error: error.message, sessions: [] };
    }
  }

  async _getSessionChrome(sessionId) {
    try {
      const result = await chrome.storage.local.get([`completed_session_${sessionId}`]);
      const session = result[`completed_session_${sessionId}`];

      if (!session) {
        return { success: false, error: 'Session not found' };
      }

      return { success: true, session };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async _deleteSessionChrome(sessionId) {
    try {
      // Remove session data
      await chrome.storage.local.remove([`completed_session_${sessionId}`]);

      // Update completed sessions list
      const result = await chrome.storage.local.get(['completedSessions']);
      const completedSessions = result.completedSessions || [];
      const updatedSessions = completedSessions.filter(session => session.id !== sessionId);
      await chrome.storage.local.set({ 'completedSessions': updatedSessions });

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async _deleteSessionsChrome(sessionIds) {
    try {
      let deletedCount = 0;

      // Remove session data
      const keysToRemove = sessionIds.map(id => `completed_session_${id}`);
      await chrome.storage.local.remove(keysToRemove);

      // Update completed sessions list
      const result = await chrome.storage.local.get(['completedSessions']);
      const completedSessions = result.completedSessions || [];
      const updatedSessions = completedSessions.filter(session => {
        if (sessionIds.includes(session.id)) {
          deletedCount++;
          return false;
        }
        return true;
      });
      await chrome.storage.local.set({ 'completedSessions': updatedSessions });

      return { success: true, deletedCount };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async _getStorageUsageChrome() {
    try {
      const usage = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES || 10485760; // 10MB default

      const result = await chrome.storage.local.get(['completedSessions']);
      const sessions = result.completedSessions || [];

      // Calculate actual session data sizes
      let totalSessionSize = 0;
      if (sessions.length > 0) {
        for (const session of sessions) {
          if (session.size) {
            totalSessionSize += session.size;
          } else {
            // Fallback: get the actual session data and calculate size
            const sessionResult = await chrome.storage.local.get([`completed_session_${session.id}`]);
            const sessionData = sessionResult[`completed_session_${session.id}`];
            if (sessionData) {
              const calculatedSize = this._calculateSessionSize(sessionData);
              totalSessionSize += calculatedSize;
            }
          }
        }
      }

      const usageData = {
        used: usage,
        quota: quota,
        available: quota - usage,
        percentage: Math.round((usage / quota) * 100),
        sessionCount: sessions.length,
        averageSessionSize: sessions.length > 0 ? Math.round(totalSessionSize / sessions.length) : 0,
        totalSessionSize: totalSessionSize
      };

      return { success: true, usage: usageData };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        usage: {
          used: 0,
          quota: 10485760,
          available: 10485760,
          percentage: 0,
          sessionCount: 0,
          averageSessionSize: 0,
          totalSessionSize: 0
        }
      };
    }
  }

  async _importSessionChrome(sessionData) {
    try {
      // Check for ID conflicts
      const result = await chrome.storage.local.get(['completedSessions']);
      const existingSessions = result.completedSessions || [];
      const existingIds = existingSessions.map(s => s.id);

      if (existingIds.includes(sessionData.id)) {
        // Generate new ID with timestamp suffix
        const timestamp = Date.now();
        sessionData.id = `${sessionData.id}_imported_${timestamp}`;
      }

      // Prepare imported session
      const importedSession = {
        ...sessionData,
        startTime: new Date(sessionData.startTime),
        endTime: sessionData.endTime ? new Date(sessionData.endTime) : null,
        completedAt: new Date(),
        size: this._calculateSessionSize(sessionData),
        status: 'imported',
        imported: true,
        importedAt: new Date()
      };

      // Save using the existing Chrome storage method
      return await this._saveSessionChrome(importedSession);
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async _cleanupChrome(options = {}) {
    try {
      const {
        maxSessions = 100,
        maxAge = 30 * 24 * 60 * 60 * 1000, // 30 days
      } = options;

      const result = await chrome.storage.local.get(['completedSessions']);
      const sessions = result.completedSessions || [];

      let sessionsToRemove = [];
      let spaceFreed = 0;

      // Remove old sessions
      const cutoffDate = new Date(Date.now() - maxAge);
      const oldSessions = sessions.filter(session =>
        new Date(session.completedAt) < cutoffDate
      );

      sessionsToRemove = sessionsToRemove.concat(oldSessions);

      // Remove excess sessions (keep most recent)
      if (sessions.length > maxSessions) {
        const sortedSessions = sessions.sort((a, b) =>
          new Date(a.completedAt) - new Date(b.completedAt)
        );
        const excessSessions = sortedSessions.slice(0, sessions.length - maxSessions);
        sessionsToRemove = sessionsToRemove.concat(excessSessions);
      }

      // Remove duplicates
      const uniqueSessionsToRemove = Array.from(
        new Set(sessionsToRemove.map(s => s.id))
      ).map(id => sessionsToRemove.find(s => s.id === id));

      // Calculate space freed
      spaceFreed = uniqueSessionsToRemove.reduce((acc, session) => acc + (session.size || 0), 0);

      // Delete sessions
      if (uniqueSessionsToRemove.length > 0) {
        const sessionIds = uniqueSessionsToRemove.map(s => s.id);
        await this._deleteSessionsChrome(sessionIds);
      }

      return {
        success: true,
        sessionsRemoved: uniqueSessionsToRemove.length,
        spaceFreed,
        corruptedRemoved: 0
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}
