/**
 * Migration Service for BugReplay Extension
 * 
 * This service handles the migration of existing data from Chrome storage
 * to IndexedDB, ensuring backward compatibility and smooth transitions.
 */

import { db, DatabaseManager } from './DatabaseSchema.js';

/**
 * Migration configuration
 */
const MIGRATION_CONFIG = {
  BATCH_SIZE: 10, // Process sessions in batches
  RETRY_ATTEMPTS: 3,
  BACKUP_KEY: 'bugreplay_migration_backup',
  MIGRATION_STATUS_KEY: 'bugreplay_migration_status'
};

/**
 * Migration status tracking
 */
export class MigrationStatus {
  constructor() {
    this.status = 'pending'; // pending, in_progress, completed, failed
    this.startTime = null;
    this.endTime = null;
    this.totalSessions = 0;
    this.migratedSessions = 0;
    this.failedSessions = 0;
    this.errors = [];
    this.backupCreated = false;
  }

  toJSON() {
    return {
      status: this.status,
      startTime: this.startTime,
      endTime: this.endTime,
      totalSessions: this.totalSessions,
      migratedSessions: this.migratedSessions,
      failedSessions: this.failedSessions,
      errors: this.errors,
      backupCreated: this.backupCreated,
      progress: this.totalSessions > 0 ? Math.round((this.migratedSessions / this.totalSessions) * 100) : 0
    };
  }

  static fromJSON(data) {
    const status = new MigrationStatus();
    Object.assign(status, data);
    return status;
  }
}

/**
 * Main Migration Service
 */
export class MigrationService {
  constructor() {
    this.status = new MigrationStatus();
  }

  /**
   * Check if migration is needed
   * @returns {Promise<boolean>} True if migration is needed
   */
  async isMigrationNeeded() {
    try {
      // Check if we have any data in Chrome storage
      const chromeData = await chrome.storage.local.get(['completedSessions']);
      const chromeSessions = chromeData.completedSessions || [];

      if (chromeSessions.length === 0) {
        return false; // No data to migrate
      }

      // Check if IndexedDB is available
      const isDBAvailable = await DatabaseManager.isAvailable();
      if (!isDBAvailable) {
        return false; // Can't migrate if IndexedDB isn't available
      }

      // Check if migration was already completed
      const migrationStatus = await this.getMigrationStatus();
      if (migrationStatus && migrationStatus.status === 'completed') {
        return false; // Already migrated
      }

      // Check if we have any sessions in IndexedDB
      const dbSessionCount = await db.sessions.count();
      if (dbSessionCount > 0 && chromeSessions.length === 0) {
        return false; // Already using IndexedDB
      }

      return true;
    } catch (error) {
      console.error('Error checking migration status:', error);
      return false;
    }
  }

  /**
   * Get current migration status
   * @returns {Promise<MigrationStatus|null>} Migration status
   */
  async getMigrationStatus() {
    try {
      const result = await chrome.storage.local.get([MIGRATION_CONFIG.MIGRATION_STATUS_KEY]);
      const statusData = result[MIGRATION_CONFIG.MIGRATION_STATUS_KEY];
      
      if (statusData) {
        return MigrationStatus.fromJSON(statusData);
      }
      
      return null;
    } catch (error) {
      console.error('Error getting migration status:', error);
      return null;
    }
  }

  /**
   * Save migration status
   * @param {MigrationStatus} status - Status to save
   */
  async saveMigrationStatus(status) {
    try {
      await chrome.storage.local.set({
        [MIGRATION_CONFIG.MIGRATION_STATUS_KEY]: status.toJSON()
      });
    } catch (error) {
      console.error('Error saving migration status:', error);
    }
  }

  /**
   * Create backup of Chrome storage data
   * @returns {Promise<boolean>} Success status
   */
  async createBackup() {
    try {
      console.log('Creating backup of Chrome storage data...');
      
      // Get all Chrome storage data
      const allData = await chrome.storage.local.get(null);
      
      // Filter BugReplay related data
      const bugReplayData = {};
      for (const [key, value] of Object.entries(allData)) {
        if (key.startsWith('completed_session_') || 
            key === 'completedSessions' ||
            key.startsWith('session_') ||
            key === 'currentSessionId' ||
            key === 'recordingState') {
          bugReplayData[key] = value;
        }
      }

      // Save backup
      const backup = {
        timestamp: new Date().toISOString(),
        data: bugReplayData,
        version: '1.0'
      };

      await chrome.storage.local.set({
        [MIGRATION_CONFIG.BACKUP_KEY]: backup
      });

      console.log('Backup created successfully');
      return true;
    } catch (error) {
      console.error('Error creating backup:', error);
      return false;
    }
  }

  /**
   * Restore from backup
   * @returns {Promise<boolean>} Success status
   */
  async restoreFromBackup() {
    try {
      console.log('Restoring from backup...');
      
      const result = await chrome.storage.local.get([MIGRATION_CONFIG.BACKUP_KEY]);
      const backup = result[MIGRATION_CONFIG.BACKUP_KEY];
      
      if (!backup) {
        console.error('No backup found');
        return false;
      }

      // Restore data
      await chrome.storage.local.set(backup.data);
      
      console.log('Backup restored successfully');
      return true;
    } catch (error) {
      console.error('Error restoring backup:', error);
      return false;
    }
  }

  /**
   * Perform the migration
   * @param {Function} progressCallback - Progress callback function
   * @returns {Promise<MigrationStatus>} Migration result
   */
  async migrate(progressCallback = null) {
    this.status = new MigrationStatus();
    this.status.status = 'in_progress';
    this.status.startTime = new Date();

    try {
      console.log('Starting migration from Chrome storage to IndexedDB...');

      // Create backup first
      this.status.backupCreated = await this.createBackup();
      await this.saveMigrationStatus(this.status);

      if (!this.status.backupCreated) {
        throw new Error('Failed to create backup');
      }

      // Get sessions from Chrome storage
      const chromeData = await chrome.storage.local.get(['completedSessions']);
      const sessionsList = chromeData.completedSessions || [];
      this.status.totalSessions = sessionsList.length;

      if (this.status.totalSessions === 0) {
        this.status.status = 'completed';
        this.status.endTime = new Date();
        await this.saveMigrationStatus(this.status);
        return this.status;
      }

      console.log(`Found ${this.status.totalSessions} sessions to migrate`);

      // Process sessions in batches
      for (let i = 0; i < sessionsList.length; i += MIGRATION_CONFIG.BATCH_SIZE) {
        const batch = sessionsList.slice(i, i + MIGRATION_CONFIG.BATCH_SIZE);
        
        for (const sessionMeta of batch) {
          try {
            await this.migrateSingleSession(sessionMeta.id);
            this.status.migratedSessions++;
          } catch (error) {
            console.error(`Failed to migrate session ${sessionMeta.id}:`, error);
            this.status.failedSessions++;
            this.status.errors.push({
              sessionId: sessionMeta.id,
              error: error.message,
              timestamp: new Date()
            });
          }

          // Update progress
          if (progressCallback) {
            progressCallback(this.status.toJSON());
          }
        }

        // Save progress periodically
        await this.saveMigrationStatus(this.status);
      }

      // Migration completed
      this.status.status = this.status.failedSessions === 0 ? 'completed' : 'completed_with_errors';
      this.status.endTime = new Date();

      console.log(`Migration completed: ${this.status.migratedSessions} migrated, ${this.status.failedSessions} failed`);

      // Clean up Chrome storage if migration was successful
      if (this.status.failedSessions === 0) {
        await this.cleanupChromeStorage();
      }

      await this.saveMigrationStatus(this.status);
      return this.status;

    } catch (error) {
      console.error('Migration failed:', error);
      this.status.status = 'failed';
      this.status.endTime = new Date();
      this.status.errors.push({
        error: error.message,
        timestamp: new Date()
      });
      
      await this.saveMigrationStatus(this.status);
      return this.status;
    }
  }

  /**
   * Migrate a single session
   * @param {string} sessionId - Session ID to migrate
   */
  async migrateSingleSession(sessionId) {
    // Get full session data from Chrome storage
    const result = await chrome.storage.local.get([`completed_session_${sessionId}`]);
    const sessionData = result[`completed_session_${sessionId}`];

    if (!sessionData) {
      throw new Error(`Session data not found for ID: ${sessionId}`);
    }

    // Convert dates to proper Date objects
    const migratedSession = {
      ...sessionData,
      startTime: new Date(sessionData.startTime),
      endTime: sessionData.endTime ? new Date(sessionData.endTime) : null,
      completedAt: sessionData.completedAt ? new Date(sessionData.completedAt) : new Date(),
      size: sessionData.size || this.calculateSessionSize(sessionData)
    };

    // Save to IndexedDB
    await db.sessions.add(migratedSession);
    
    console.log(`Migrated session: ${sessionId}`);
  }

  /**
   * Clean up Chrome storage after successful migration
   */
  async cleanupChromeStorage() {
    try {
      console.log('Cleaning up Chrome storage after migration...');
      
      // Get all keys to remove
      const allData = await chrome.storage.local.get(null);
      const keysToRemove = [];
      
      for (const key of Object.keys(allData)) {
        if (key.startsWith('completed_session_') || key === 'completedSessions') {
          keysToRemove.push(key);
        }
      }

      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
        console.log(`Removed ${keysToRemove.length} keys from Chrome storage`);
      }
    } catch (error) {
      console.error('Error cleaning up Chrome storage:', error);
    }
  }

  /**
   * Calculate session size for migration
   * @param {Object} sessionData - Session data
   * @returns {number} Size in bytes
   */
  calculateSessionSize(sessionData) {
    let size = 0;
    
    try {
      size += JSON.stringify(sessionData.logs || []).length;
      size += (sessionData.screenshots || []).reduce((acc, screenshot) => {
        if (typeof screenshot === 'string') {
          return acc + screenshot.length;
        } else if (screenshot && screenshot.dataUrl) {
          return acc + screenshot.dataUrl.length;
        }
        return acc;
      }, 0);
      size += JSON.stringify(sessionData.harData || {}).length;
    } catch (error) {
      console.warn('Error calculating session size during migration:', error);
    }

    return size;
  }

  /**
   * Verify migration integrity
   * @returns {Promise<Object>} Verification result
   */
  async verifyMigration() {
    try {
      const chromeData = await chrome.storage.local.get(['completedSessions']);
      const chromeSessions = chromeData.completedSessions || [];
      
      const dbSessionCount = await db.sessions.count();
      
      const result = {
        success: true,
        chromeSessionCount: chromeSessions.length,
        dbSessionCount: dbSessionCount,
        migrationComplete: chromeSessions.length === 0 && dbSessionCount > 0
      };

      console.log('Migration verification:', result);
      return result;
    } catch (error) {
      console.error('Error verifying migration:', error);
      return { success: false, error: error.message };
    }
  }
}
