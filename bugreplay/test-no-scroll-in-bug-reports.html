<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay - Test: No Scroll Events in Bug Reports</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .section {
            margin: 40px 0;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 400px;
        }
        .button {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        .button.secondary {
            background: #2196F3;
        }
        .button.secondary:hover {
            background: #1976D2;
        }
        .button.danger {
            background: #f44336;
        }
        .button.danger:hover {
            background: #d32f2f;
        }
        .instructions {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .expected-result {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .scroll-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        h2 {
            color: #FFD700;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body>
    <div class="scroll-indicator" id="scrollIndicator">
        Scroll: 0%
    </div>

    <div class="container">
        <h1>🐛 BugReplay - Test: No Scroll Events in Bug Reports</h1>
        
        <div class="instructions">
            <h2>📋 Test Instructions</h2>
            <p><strong>Purpose:</strong> Verify that scroll events are NOT included in the "Steps to Reproduce" section of bug reports.</p>
            <ol>
                <li><strong>Start BugReplay recording</strong></li>
                <li><strong>Click the buttons below</strong> (these SHOULD appear in bug report)</li>
                <li><strong>Scroll through this page</strong> (this should NOT appear in bug report)</li>
                <li><strong>Click more buttons</strong> after scrolling</li>
                <li><strong>Stop recording</strong> and generate bug report</li>
                <li><strong>Check "Steps to Reproduce"</strong> - should only show button clicks, no scroll events</li>
            </ol>
        </div>

        <div class="expected-result">
            <h2>✅ Expected Bug Report Result</h2>
            <p><strong>Steps to Reproduce should contain:</strong></p>
            <ul>
                <li>✅ Button clicks (e.g., "Clicked 'Start Test' button")</li>
                <li>✅ Form interactions</li>
                <li>✅ Link clicks</li>
                <li>❌ NO scroll events (e.g., "Scrolled down to 25% of page")</li>
            </ul>
        </div>

        <div class="section">
            <h2>🎯 Section 1 - Top Actions</h2>
            <p>Click these buttons before scrolling. They should appear in the bug report.</p>
            <button class="button" onclick="logAction('Clicked Start Test button in Section 1')">
                Start Test
            </button>
            <button class="button secondary" onclick="logAction('Clicked Configure button in Section 1')">
                Configure
            </button>
            <p>Now scroll down to the next section. The scroll action should NOT appear in the bug report.</p>
        </div>

        <div class="section">
            <h2>🎯 Section 2 - Middle Actions</h2>
            <p>You scrolled to get here, but that scroll should not be in the bug report. These button clicks should be included:</p>
            <button class="button" onclick="logAction('Clicked Process Data button in Section 2')">
                Process Data
            </button>
            <button class="button secondary" onclick="logAction('Clicked Validate button in Section 2')">
                Validate
            </button>
            <p>Continue scrolling down. Remember, scroll events should be excluded from bug reports.</p>
        </div>

        <div class="section">
            <h2>🎯 Section 3 - Bottom Actions</h2>
            <p>More scrolling brought you here. These final actions should appear in the bug report:</p>
            <button class="button" onclick="logAction('Clicked Save Results button in Section 3')">
                Save Results
            </button>
            <button class="button danger" onclick="logAction('Clicked Delete All button in Section 3')">
                Delete All
            </button>
            <button class="button secondary" onclick="logAction('Clicked Finish Test button in Section 3')">
                Finish Test
            </button>
        </div>

        <div class="section">
            <h2>📊 Test Verification</h2>
            <p>After completing the test above:</p>
            <ol>
                <li><strong>Stop BugReplay recording</strong></li>
                <li><strong>Generate a bug report</strong></li>
                <li><strong>Check the "Steps to Reproduce" section</strong></li>
                <li><strong>Verify it contains:</strong>
                    <ul>
                        <li>✅ Button click actions</li>
                        <li>❌ NO scroll events</li>
                    </ul>
                </li>
            </ol>
            
            <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">
                <h3>Expected Steps to Reproduce:</h3>
                <pre style="color: #90EE90; font-family: monospace;">
1. Clicked 'Start Test' button in Section 1
2. Clicked 'Configure' button in Section 1
3. Clicked 'Process Data' button in Section 2
4. Clicked 'Validate' button in Section 2
5. Clicked 'Save Results' button in Section 3
6. Clicked 'Delete All' button in Section 3
7. Clicked 'Finish Test' button in Section 3
                </pre>
                <p style="color: #FFB6C1;"><strong>Note:</strong> No "Scrolled down to X% of page" entries should appear!</p>
            </div>
        </div>
    </div>

    <script>
        // Update scroll indicator
        function updateScrollIndicator() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrollPercent = scrollHeight > 0 ? Math.round((scrollTop / scrollHeight) * 100) : 0;
            
            document.getElementById('scrollIndicator').textContent = `Scroll: ${scrollPercent}%`;
        }

        // Log actions for testing
        function logAction(message) {
            console.log(`BugReplay Test Action: ${message}`);
            
            // Visual feedback
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '✓ Clicked!';
            button.style.background = '#4CAF50';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '';
            }, 1000);
        }

        // Track scroll events for verification
        window.addEventListener('scroll', function() {
            updateScrollIndicator();
        });

        // Initialize
        updateScrollIndicator();
        console.log('BugReplay Test: Page loaded - scroll events should NOT appear in bug reports');
        console.log('BugReplay Test: Only button clicks should appear in Steps to Reproduce');
    </script>
</body>
</html>
