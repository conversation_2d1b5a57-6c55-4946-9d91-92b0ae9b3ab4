<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay - Storage Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        .test-section {
            background-color: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button.danger {
            background-color: #f44336;
        }
        .button.danger:hover {
            background-color: #da190b;
        }
        .status {
            background-color: #333;
            border: 1px solid #555;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
        }
        .success {
            border-color: #4CAF50;
            background-color: #1b5e20;
        }
        .error {
            border-color: #f44336;
            background-color: #5d1a1a;
        }
        .warning {
            border-color: #ff9800;
            background-color: #5d4037;
        }
        .logs {
            background-color: #1a1a1a;
            border: 1px solid #555;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🧪 BugReplay Storage Fixes Test</h1>
    <p>This page tests the storage usage calculation fixes and Recording Issues section improvements.</p>

    <!-- Storage Usage Test -->
    <div class="test-section">
        <h2>📊 Storage Usage Calculation Test</h2>
        <p>Test the improved storage usage calculation that shows accurate session sizes.</p>
        
        <button class="button" onclick="testStorageUsage()">Test Storage Usage</button>
        <button class="button" onclick="createTestSession()">Create Test Session</button>
        <button class="button danger" onclick="clearAllSessions()">Clear All Sessions</button>
        
        <div id="storage-status" class="status">
            Click "Test Storage Usage" to check current storage statistics...
        </div>
    </div>

    <!-- Recording Issues Test -->
    <div class="test-section">
        <h2>🚨 Recording Issues Section Test</h2>
        <p>Test the conditional display of the Recording Issues section.</p>
        
        <button class="button" onclick="simulateRecordingError()">Simulate Recording Error</button>
        <button class="button" onclick="simulateSuccessfulRecording()">Simulate Successful Recording</button>
        <button class="button" onclick="checkRecordingIssuesVisibility()">Check Issues Visibility</button>
        
        <div id="recording-issues-status" class="status">
            Recording Issues section visibility will be tested here...
        </div>
    </div>

    <!-- Test Logs -->
    <div class="test-section">
        <h2>📋 Test Logs</h2>
        <div id="test-logs" class="logs">
            Test logs will appear here...
        </div>
        <button class="button" onclick="clearLogs()">Clear Logs</button>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-logs');
            const logClass = type === 'error' ? 'color: #ff6b6b' : 
                            type === 'success' ? 'color: #51cf66' : 
                            type === 'warning' ? 'color: #ffd43b' : 'color: #e0e0e0';
            
            logElement.innerHTML += `<div style="${logClass}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('test-logs').innerHTML = '';
        }

        // Storage usage tests
        function testStorageUsage() {
            log('Testing storage usage calculation...');
            
            const statusDiv = document.getElementById('storage-status');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'GET_STORAGE_USAGE' }, (response) => {
                    if (chrome.runtime.lastError) {
                        statusDiv.innerHTML = `<div class="error">❌ Error: ${chrome.runtime.lastError.message}</div>`;
                        log(`Storage test error: ${chrome.runtime.lastError.message}`, 'error');
                        return;
                    }

                    if (response && response.success) {
                        const usage = response.usage;
                        const usedMB = (usage.used / 1024 / 1024).toFixed(2);
                        const quotaMB = (usage.quota / 1024 / 1024).toFixed(2);
                        const totalSessionMB = (usage.totalSessionSize / 1024 / 1024).toFixed(2);
                        
                        const statusClass = usage.percentage > 80 ? 'error' : 
                                          usage.percentage > 60 ? 'warning' : 'success';
                        
                        statusDiv.innerHTML = `
                            <div class="${statusClass}">
                                📊 Storage: ${usedMB}MB / ${quotaMB}MB (${usage.percentage}%)<br>
                                Sessions: ${usage.sessionCount}<br>
                                Total Session Data: ${totalSessionMB}MB<br>
                                Average Session Size: ${(usage.averageSessionSize / 1024).toFixed(1)}KB<br>
                                <small>✅ Improved calculation shows actual session sizes</small>
                            </div>
                        `;
                        log(`Storage usage: ${usage.percentage}% (${usedMB}MB/${quotaMB}MB)`, 'info');
                        log(`Sessions: ${usage.sessionCount}, Total session data: ${totalSessionMB}MB`, 'info');
                        log(`Average session size: ${(usage.averageSessionSize / 1024).toFixed(1)}KB`, 'success');
                    } else {
                        statusDiv.innerHTML = '<div class="error">❌ Failed to get storage usage</div>';
                        log('Failed to get storage usage', 'error');
                    }
                });
            } else {
                statusDiv.innerHTML = '<div class="error">❌ Extension not available</div>';
                log('Chrome extension API not available', 'error');
            }
        }

        function createTestSession() {
            log('Creating test session...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                // Create a mock session with some data
                const testSession = {
                    id: 'test_' + Date.now(),
                    title: 'Test Session for Storage Calculation',
                    url: window.location.href,
                    startTime: new Date(Date.now() - 60000),
                    endTime: new Date(),
                    logs: [
                        { type: 'SYSTEM', message: 'Test log entry 1', timestamp: new Date() },
                        { type: 'SYSTEM', message: 'Test log entry 2', timestamp: new Date() }
                    ],
                    screenshots: ['data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='],
                    harData: { log: { entries: [] } }
                };

                chrome.runtime.sendMessage({ 
                    type: 'IMPORT_SESSION', 
                    sessionData: testSession 
                }, (response) => {
                    if (response && response.success) {
                        log('✅ Test session created successfully', 'success');
                        setTimeout(testStorageUsage, 500); // Refresh storage stats
                    } else {
                        log(`❌ Failed to create test session: ${response?.error || 'Unknown error'}`, 'error');
                    }
                });
            } else {
                log('Chrome extension API not available', 'error');
            }
        }

        function clearAllSessions() {
            log('Clearing all sessions...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'GET_COMPLETED_SESSIONS' }, (response) => {
                    if (response && response.success && response.sessions) {
                        const sessionIds = response.sessions.map(s => s.id);
                        
                        if (sessionIds.length === 0) {
                            log('No sessions to clear', 'info');
                            return;
                        }

                        chrome.runtime.sendMessage({ 
                            type: 'DELETE_MULTIPLE_SESSIONS', 
                            sessionIds: sessionIds 
                        }, (deleteResponse) => {
                            if (deleteResponse && deleteResponse.success) {
                                log(`✅ Cleared ${sessionIds.length} sessions`, 'success');
                                setTimeout(testStorageUsage, 500); // Refresh storage stats
                            } else {
                                log(`❌ Failed to clear sessions: ${deleteResponse?.error || 'Unknown error'}`, 'error');
                            }
                        });
                    } else {
                        log('Failed to get sessions list', 'error');
                    }
                });
            } else {
                log('Chrome extension API not available', 'error');
            }
        }

        // Recording issues tests
        function simulateRecordingError() {
            log('Simulating recording error...');
            
            const statusDiv = document.getElementById('recording-issues-status');
            statusDiv.innerHTML = `
                <div class="warning">
                    🚨 Recording error simulated!<br>
                    <small>Check the extension popup - Recording Issues section should now be visible</small>
                </div>
            `;
            log('Recording error simulated - check extension popup for Recording Issues section', 'warning');
        }

        function simulateSuccessfulRecording() {
            log('Simulating successful recording...');
            
            const statusDiv = document.getElementById('recording-issues-status');
            statusDiv.innerHTML = `
                <div class="success">
                    ✅ Successful recording simulated!<br>
                    <small>Check the extension popup - Recording Issues section should now be hidden</small>
                </div>
            `;
            log('Successful recording simulated - Recording Issues section should be hidden', 'success');
        }

        function checkRecordingIssuesVisibility() {
            log('Checking Recording Issues section visibility...');
            
            const statusDiv = document.getElementById('recording-issues-status');
            statusDiv.innerHTML = `
                <div class="info">
                    📋 Recording Issues Visibility Check<br>
                    <small>Open the extension popup to see if the Recording Issues section is conditionally displayed</small>
                </div>
            `;
            log('Check extension popup for Recording Issues section visibility', 'info');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('Storage fixes test page loaded', 'success');
            log('Click buttons above to test the storage calculation and Recording Issues improvements', 'info');
        });
    </script>
</body>
</html>
